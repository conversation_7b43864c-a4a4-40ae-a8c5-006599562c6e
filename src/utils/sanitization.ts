/**
 * Input sanitization utilities for security
 */

/**
 * Sanitize string input to prevent XSS attacks
 * @param input Raw string input
 * @returns Sanitized string
 */
export const sanitizeString = (input: string): string => {
  if (typeof input !== 'string') return ''
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .substring(0, 1000) // Limit length
}

/**
 * Sanitize email input
 * @param email Email string
 * @returns Sanitized email
 */
export const sanitizeEmail = (email: string): string => {
  if (typeof email !== 'string') return ''
  
  return email
    .trim()
    .toLowerCase()
    .replace(/[^\w@.-]/g, '') // Only allow word chars, @, ., -
    .substring(0, 254) // RFC 5321 limit
}

/**
 * Sanitize name input (first name, last name, etc.)
 * @param name Name string
 * @returns Sanitized name
 */
export const sanitizeName = (name: string): string => {
  if (typeof name !== 'string') return ''
  
  return name
    .trim()
    .replace(/[<>]/g, '') // Remove HTML tags
    .replace(/[^\w\s'-]/g, '') // Only allow word chars, spaces, apostrophes, hyphens
    .substring(0, 100) // Reasonable name length limit
}

/**
 * Validate and sanitize URL input
 * @param url URL string
 * @returns Sanitized URL or empty string if invalid
 */
export const sanitizeUrl = (url: string): string => {
  if (typeof url !== 'string') return ''
  
  try {
    const parsed = new URL(url.trim())
    // Only allow http and https protocols
    if (!['http:', 'https:'].includes(parsed.protocol)) {
      return ''
    }
    return parsed.toString()
  } catch {
    return ''
  }
}

/**
 * Sanitize search query input
 * @param query Search query string
 * @returns Sanitized query
 */
export const sanitizeSearchQuery = (query: string): string => {
  if (typeof query !== 'string') return ''
  
  return query
    .trim()
    .replace(/[<>]/g, '') // Remove HTML tags
    .replace(/[^\w\s-]/g, '') // Only allow word chars, spaces, hyphens
    .substring(0, 200) // Limit search query length
}

/**
 * Sanitize numeric input
 * @param input Numeric input (string or number)
 * @returns Sanitized number or null if invalid
 */
export const sanitizeNumber = (input: string | number): number | null => {
  if (typeof input === 'number') {
    return isFinite(input) ? input : null
  }
  
  if (typeof input === 'string') {
    const num = parseFloat(input.trim())
    return isFinite(num) ? num : null
  }
  
  return null
}

/**
 * Sanitize boolean input
 * @param input Boolean input (any type)
 * @returns Boolean value
 */
export const sanitizeBoolean = (input: unknown): boolean => {
  if (typeof input === 'boolean') return input
  if (typeof input === 'string') {
    return ['true', '1', 'yes', 'on'].includes(input.toLowerCase().trim())
  }
  if (typeof input === 'number') return input !== 0
  return false
}
