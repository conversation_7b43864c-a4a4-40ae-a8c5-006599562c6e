// Optionnel : Stocker les statistiques de consentement en base de données
import { supabase } from '../lib/supabase';

interface CookieConsentStats {
  user_id?: string;
  session_id: string;
  consent_date: string;
  essential: boolean;
  analytics: boolean;
  marketing: boolean;
  user_agent: string;
  country?: string;
}

export class CookieAnalytics {

  // Enregistrer les statistiques de consentement (anonymisées)
  static async recordConsentStats(preferences: Record<string, boolean>) {
    try {
      const stats: CookieConsentStats = {
        session_id: this.generateSessionId(),
        consent_date: new Date().toISOString(),
        essential: preferences.essential || false,
        analytics: preferences.analytics || false,
        marketing: preferences.marketing || false,
        user_agent: navigator.userAgent,
        // country: await this.getCountry() // Optionnel via IP
      };

      // Stocker en base (table anonyme)
      const { error } = await supabase
        .from('cookie_consent_stats')
        .insert([stats]);

      if (error) {
        console.error('Erreur stockage stats cookies:', error);
      }
    } catch (error) {
      console.error('Erreur analytics cookies:', error);
    }
  }

  // Récupérer les statistiques pour les admins
  static async getConsentStats() {
    try {
      const { data, error } = await supabase
        .from('cookie_consent_stats')
        .select(`
          consent_date,
          essential,
          analytics,
          marketing,
          country
        `)
        .order('consent_date', { ascending: false })
        .limit(1000);

      if (error) throw error;

      // Calculer les pourcentages
      const total = data.length;
      const analyticsAcceptance = data.filter(d => d.analytics).length;
      const marketingAcceptance = data.filter(d => d.marketing).length;

      return {
        total_consents: total,
        analytics_acceptance_rate: Math.round((analyticsAcceptance / total) * 100),
        marketing_acceptance_rate: Math.round((marketingAcceptance / total) * 100),
        raw_data: data
      };
    } catch (error) {
      console.error('Erreur récupération stats:', error);
      return null;
    }
  }

  private static generateSessionId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}

// SQL pour créer la table (à exécuter dans Supabase)
export const cookieStatsTableSQL = `
CREATE TABLE IF NOT EXISTS cookie_consent_stats (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(50) NOT NULL,
  consent_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  essential BOOLEAN DEFAULT true,
  analytics BOOLEAN DEFAULT false,
  marketing BOOLEAN DEFAULT false,
  user_agent TEXT,
  country VARCHAR(10),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour les performances
CREATE INDEX idx_cookie_stats_date ON cookie_consent_stats(consent_date);
CREATE INDEX idx_cookie_stats_country ON cookie_consent_stats(country);

-- RLS pour sécurité (seulement admins peuvent lire)
ALTER TABLE cookie_consent_stats ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Admins can view cookie stats" ON cookie_consent_stats
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role IN ('admin', 'super_admin')
    )
  );
`;
