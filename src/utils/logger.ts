// Utilitaire de logging sécurisé - contrôle granulaire des logs

const isDevelopment = process.env.NODE_ENV === 'development';

// Mode silencieux : même en dev, on peut vouloir moins de logs
const isQuietMode = true; // Mettre à false pour activer le debug détaillé

/**
 * Logger sécurisé avec contrôle de verbosité
 */
export const secureLog = {
  info: (message: string, data?: any) => {
    if (isDevelopment && !isQuietMode) {
      console.log(message, data);
    }
    // En production ou mode quiet : RIEN
  },

  warn: (message: string, data?: any) => {
    if (isDevelopment && !isQuietMode) {
      console.warn(message, data);
    }
    // En production ou mode quiet : RIEN
  },

  error: (message: string, error?: any) => {
    if (isDevelopment) {
      console.error(message, error);
    }
    // En production : RIEN (ou utiliser un service comme Sentry)
  },

  user: (message: string, userId?: string, additionalData?: any) => {
    if (isDevelopment && !isQuietMode) {
      console.log(message, { userId, ...additionalData });
    }
    // En production ou mode quiet : RIEN
  },

  // Log important qui passe même en mode quiet
  important: (message: string, data?: any) => {
    if (isDevelopment) {
      console.log('🎯', message, data);
    }
  }
};

/**
 * Pour les cas où on a vraiment besoin de logger une erreur critique en production
 * (à utiliser avec parcimonie, idéalement avec un service externe comme Sentry)
 */
export const criticalError = (message: string, error?: any) => {
  if (isDevelopment) {
    console.error('🚨 CRITICAL:', message, error);
  } else {
    // En production, on pourrait envoyer à un service de monitoring
    // console.error(`Critical error: ${message}`);
    // Ou mieux : Sentry.captureException(error);
  }
};
