/**
 * Utility for customizing Supabase authentication error messages
 */

export const getAuthErrorMessage = (error: any): string => {
  if (!error?.message) return 'An unexpected error occurred'

  const message = error.message.toLowerCase()

  // Login errors
  if (message.includes('invalid login credentials') ||
      message.includes('invalid credentials') ||
      message.includes('email not confirmed')) {
    return 'Invalid email or password. Please check your credentials.'
  }

  // Registration errors
  if (message.includes('user already registered') ||
      message.includes('email already exists') ||
      message.includes('already registered')) {
    return 'An account already exists with this email address. Please sign in instead.'
  }

  // Validation errors
  if (message.includes('email not found') || message.includes('user not found')) {
    return 'No account found with this email address.'
  }

  if (message.includes('invalid email') || message.includes('email not valid')) {
    return 'The email address is not valid.'
  }

  if (message.includes('password too short') || message.includes('password must be')) {
    return 'Password must be at least 6 characters long.'
  }

  // Security errors
  if (message.includes('too many requests')) {
    return 'Too many login attempts. Please wait a few minutes before trying again.'
  }

  if (message.includes('signup disabled') || message.includes('signups not allowed')) {
    return 'Registration is currently disabled. Please contact your administrator.'
  }

  if (message.includes('access denied') || message.includes('not authorized')) {
    return 'This email is not authorized to access the application. Please contact your administrator.'
  }

  // Network errors
  if (message.includes('network') || message.includes('fetch')) {
    return 'Connection problem. Please check your internet connection.'
  }

  // Email confirmation errors
  if (message.includes('email not confirmed')) {
    return 'Please confirm your email before signing in.'
  }

  // Default message
  return 'Authentication error. Please try again.'
}

/**
 * Specific error messages for login
 */
export const getLoginErrorMessage = (error: any): string => {
  if (!error?.message) return 'An unexpected error occurred'

  const message = error.message.toLowerCase()

  // ✅ Message générique pour éviter user enumeration
  if (message.includes('invalid login credentials') ||
      message.includes('invalid credentials') ||
      message.includes('email not found') ||
      message.includes('user not found') ||
      message.includes('no user found')) {
    return 'Invalid email or password. Please check your credentials.'
  }

  return getAuthErrorMessage(error)
}

/**
 * Specific error messages for registration
 */
export const getSignUpErrorMessage = (error: any): string => {
  if (!error?.message) return 'An unexpected error occurred'

  const message = error.message.toLowerCase()

  console.log('🔍 SignUp Error Details:', error) // Debug log

  // Various ways Supabase can indicate duplicate email
  if (message.includes('user already registered') ||
      message.includes('email already exists') ||
      message.includes('already registered') ||
      message.includes('duplicate') ||
      message.includes('already in use') ||
      message.includes('email address is already registered') ||
      message.includes('user with this email already exists') ||
      error.code === 'EMAIL_ADDRESS_ALREADY_IN_USE' ||
      error.status === 422) {
    return 'This email is already in use. An account already exists with this email address. Please sign in or use a different email.'
  }

  // Rate limiting
  if (message.includes('email rate limit exceeded') ||
      message.includes('too many requests') ||
      message.includes('rate limit')) {
    return 'Too many signup attempts. Please wait a few minutes before trying again.'
  }

  // Invalid email format
  if (message.includes('invalid email') ||
      message.includes('email not valid') ||
      message.includes('malformed email')) {
    return 'The email format is not valid.'
  }

  // Password issues
  if (message.includes('password') && message.includes('weak')) {
    return 'Password is too weak. Use at least 6 characters.'
  }

  // Signups disabled
  if (message.includes('signup') && message.includes('disabled')) {
    return 'Signups are temporarily disabled. Please contact the administrator.'
  }

  return getAuthErrorMessage(error)
}

/**
 * Check if error suggests user should register instead
 */
export const shouldSuggestRegistration = (error: any): boolean => {
  if (!error?.message) return false

  const message = error.message.toLowerCase()

  return message.includes('email not found') ||
         message.includes('user not found') ||
         message.includes('no user found')
}
