/**
 * Rate Limiter sécurisé pour protéger contre les attaques de force brute
 */
class RateLimiter {
  private attempts = new Map<string, { count: number; resetTime: number }>()

  /**
   * Vérifie si une tentative est autorisée
   * @param identifier Identifiant unique (email, IP, etc.)
   * @param maxAttempts Nombre maximum de tentatives (défaut: 5)
   * @param windowMs Fenêtre de temps en millisecondes (défaut: 15 minutes)
   * @returns true si autorisé, false si limité
   */
  checkAttempt(identifier: string, maxAttempts = 5, windowMs = 15 * 60 * 1000): boolean {
    const now = Date.now()
    const key = identifier.toLowerCase()

    const current = this.attempts.get(key)

    // Première tentative ou fenêtre expirée
    if (!current || now > current.resetTime) {
      this.attempts.set(key, { count: 1, resetTime: now + windowMs })
      return true
    }

    // Trop de tentatives
    if (current.count >= maxAttempts) {
      return false
    }

    // Incrémenter le compteur
    current.count++
    return true
  }

  /**
   * Obtient le temps restant avant de pouvoir réessayer
   * @param identifier Identifiant unique
   * @returns Temps restant en secondes
   */
  getRemainingTime(identifier: string): number {
    const current = this.attempts.get(identifier.toLowerCase())
    if (!current) return 0

    const remaining = current.resetTime - Date.now()
    return Math.max(0, Math.ceil(remaining / 1000))
  }

  /**
   * Réinitialise le compteur pour un identifiant (après connexion réussie)
   * @param identifier Identifiant unique
   */
  reset(identifier: string): void {
    this.attempts.delete(identifier.toLowerCase())
  }

  /**
   * Nettoie les entrées expirées (appelé périodiquement)
   */
  cleanup(): void {
    const now = Date.now()
    for (const [key, value] of this.attempts.entries()) {
      if (now > value.resetTime) {
        this.attempts.delete(key)
      }
    }
  }
}

// Instances spécialisées
export const loginRateLimiter = new RateLimiter()
export const signupRateLimiter = new RateLimiter()

// Nettoyage automatique toutes les heures
setInterval(() => {
  loginRateLimiter.cleanup()
  signupRateLimiter.cleanup()
}, 60 * 60 * 1000)
