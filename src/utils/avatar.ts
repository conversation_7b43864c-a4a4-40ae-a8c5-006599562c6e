/**
 * Generate avatar URL using a service like UI Avatars or DiceBear
 * @param firstName First name of the user
 * @param lastName Last name of the user
 * @returns URL for the avatar image
 */
export const generateAvatarUrl = (firstName: string, lastName: string): string => {
  // Use UI Avatars service for simple text-based avatars
  const initials = `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  const backgroundColor = '8866a7'; // Purple theme color
  const color = 'ffffff'; // White text

  return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=${backgroundColor}&color=${color}&size=128&bold=true`;
};

/**
 * Generate avatar URL with full name
 * @param fullName Full name of the user
 * @returns URL for the avatar image
 */
export const generateAvatarUrlFromName = (fullName: string): string => {
  const names = fullName.trim().split(' ');
  const firstName = names[0] || 'U';
  const lastName = names[1] || 'ser';

  return generateAvatarUrl(firstName, lastName);
};
