import { supabase } from '../lib/supabase';

/**
 * Validate email with domain restrictions using database
 * @param email Email address to validate
 * @returns Promise with validation result
 */
export const validateEmailWithDomain = async (email: string): Promise<{ isValid: boolean; error?: string }> => {
  // ✅ Pas de logs d'emails en production
  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 Validating email format...');
  }

  // Basic email format validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  if (!email) {
    if (process.env.NODE_ENV === 'development') {
      console.log('❌ Email is empty');
    }
    return { isValid: false, error: 'Email is required' };
  }

  if (!emailRegex.test(email)) {
    if (process.env.NODE_ENV === 'development') {
      console.log('❌ Email format invalid');
    }
    return { isValid: false, error: 'Please enter a valid email address' };
  }

  try {
    const emailLower = email.toLowerCase();

    // ✅ Pas de logs d'emails en production
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 Checking authorization...');
    }

    // Use secure RPC function to validate email (respects RLS policies)
    const { data: isAllowed, error: rpcError } = await supabase.rpc('validate_email_allowed_secure', {
      email_to_check: emailLower
    });

    if (rpcError) {
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ RPC validation error:', rpcError);
      }
      return {
        isValid: false,
        error: 'Unable to validate email. Please try again later.'
      };
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('📊 RPC validation result:', isAllowed);
    }

    if (isAllowed === true) {
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ Email authorized via RPC');
      }
      return { isValid: true };
    } else {
      if (process.env.NODE_ENV === 'development') {
        console.log('❌ Email not authorized via RPC');
      }
      return {
        isValid: false,
        error: 'Your email is not authorized, please contact Zest'
      };
    }

  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Exception in email validation:', error);
    }
    return {
      isValid: false,
      error: 'Unable to validate email. Please try again later.'
    };
  }
};



/**
 * Get email suggestions based on common typos
 * @param email Email address to suggest corrections for
 * @returns Array of suggested email corrections
 */
export const getEmailSuggestions = (email: string): string[] => {
  if (!email.includes('@')) return [];

  const [localPart, domain] = email.split('@');
  const suggestions: string[] = [];

  // Common domain typos
  const domainSuggestions: { [key: string]: string } = {
    'gmial.com': 'gmail.com',
    'gmai.com': 'gmail.com',
    'gmail.co': 'gmail.com',
    'outlok.com': 'outlook.com',
    'outloo.com': 'outlook.com',
    'outlook.co': 'outlook.com',
  };

  if (domain && domainSuggestions[domain.toLowerCase()]) {
    suggestions.push(`${localPart}@${domainSuggestions[domain.toLowerCase()]}`);
  }

  return suggestions;
};

/**
 * Get text describing allowed domains for user information
 * @returns Promise with human-readable text about allowed domains
 */
export const getAllowedDomainsText = async (): Promise<string> => {
  return 'Only authorized email addresses can access this platform. Please contact Zest if you need access.';
};

/**
 * Check if an email domain is explicitly allowed
 * @param email Email address to check
 * @returns Promise with true if domain is in the allowed list
 */
export const isDomainAllowed = async (email: string): Promise<boolean> => {
  try {
    const domain = email.split('@')[1]?.toLowerCase();

    if (!domain) {
      return false;
    }

    const { data: domainData, error } = await supabase
      .from('allowed_emails')
      .select('id')
      .eq('email_pattern', domain)
      .eq('pattern_type', 'domain')
      .eq('is_active', true)
      .limit(1);

    if (error) {
      console.error('Error checking domain:', error);
      return false;
    }

    return domainData && domainData.length > 0;
  } catch (error) {
    console.error('Exception checking domain:', error);
    return false;
  }
};
