// Utilitaires pour gérer les analytics selon les préférences cookies

declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
    dataLayer?: any[];
  }
}

interface CookieConsent {
  hasConsented: boolean;
  preferences: Record<string, boolean>;
}

export class AnalyticsManager {
  private consent: CookieConsent | null = null;

  constructor() {
    this.loadConsent();
  }

  private loadConsent() {
    const storedConsent = localStorage.getItem('zest_cookie_consent');
    if (storedConsent) {
      this.consent = JSON.parse(storedConsent);
    }
  }

  // Vérifier si les analytics sont autorisés
  canUseAnalytics(): boolean {
    return this.consent?.preferences?.analytics === true;
  }

  // Initialiser Google Analytics seulement si autorisé
  initializeGoogleAnalytics(trackingId: string) {
    if (!this.canUseAnalytics()) {
      console.log('📊 Analytics non autorisés par l\'utilisateur');
      return;
    }

    // Charger Google Analytics
    const script = document.createElement('script');
    script.src = `https://www.googletagmanager.com/gtag/js?id=${trackingId}`;
    script.async = true;
    document.head.appendChild(script);

    // Initialiser gtag
    window.dataLayer = window.dataLayer || [];
    window.gtag = function(...args: any[]) {
      window.dataLayer!.push(args);
    };

    window.gtag('js', new Date());
    window.gtag('config', trackingId, {
      // Respecter les préférences de cookies
      anonymize_ip: true,
      allow_google_signals: this.consent?.preferences?.marketing === true
    });

    console.log('📊 Google Analytics initialisé');
  }

  // Tracker des événements (seulement si autorisé)
  trackEvent(eventName: string, parameters?: Record<string, any>) {
    if (!this.canUseAnalytics() || !window.gtag) {
      return;
    }

    window.gtag('event', eventName, {
      event_category: 'ZEST_Companion',
      event_label: parameters?.label,
      value: parameters?.value,
      ...parameters
    });
  }

  // Tracker les vues de page
  trackPageView(page: string) {
    if (!this.canUseAnalytics() || !window.gtag) {
      return;
    }

    window.gtag('config', 'GA_TRACKING_ID', {
      page_path: page
    });
  }

  // Tracker l'utilisation des modules d'apprentissage
  trackModuleUsage(moduleId: string, action: 'start' | 'complete' | 'exit') {
    this.trackEvent('module_interaction', {
      module_id: moduleId,
      action: action,
      label: `Module ${moduleId} - ${action}`
    });
  }

  // Tracker les conversations de chat
  trackChatInteraction(themeId: number, messageCount: number) {
    this.trackEvent('chat_session', {
      theme_id: themeId,
      message_count: messageCount,
      label: `Chat Theme ${themeId}`
    });
  }
}

// Instance globale
export const analytics = new AnalyticsManager();
