interface Document {
  content: string;
  source_file: string;
  similarity: number;
}

export async function searchSimilarDocuments(query: string): Promise<Document[]> {
  try {
    const response = await fetch('http://localhost:3000/api/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query }),
    });

    if (!response.ok) {
      throw new Error('Network response was not ok');
    }

    const data = await response.json();
    return data.documents;
  } catch (error) {
    console.error('Error:', error);
    return [];
  }
}

export async function generateRAGResponse(query: string, theme: string, subTheme: { title: string, description: string }) {
  try {
    // Utiliser l'API backend pour générer la réponse au lieu d'OpenAI directement
    const response = await fetch('http://localhost:3000/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        theme,
        subTheme: {
          title: subTheme.title,
          description: subTheme.description
        }
      }),
    });

    if (!response.ok) {
      throw new Error('Network response was not ok');
    }

    const data = await response.json();
    return data.response || "Sorry, I couldn't generate a response.";

  } catch (error) {
    console.error('Error:', error);
    return "Sorry, an error occurred while generating the response. Please ensure the backend server is running.";
  }
}
