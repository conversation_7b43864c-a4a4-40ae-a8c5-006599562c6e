import React, { useState, useEffect } from 'react';
import { Theme, SubTheme } from '../types';
import { FolderIcon, FileIcon, ChevronDownIcon, ChevronRightIcon, LockIcon, XIcon, FileTextIcon, SearchIcon, PlayIcon } from 'lucide-react';
import Footer from '../components/Footer';
import Breadcrumbs, { BreadcrumbItem } from '../components/Breadcrumbs';

interface LibraryProps {
  themes: Theme[];
  subThemes: SubTheme[];
}

interface Document {
  name: string;
  path: string;
  type: 'pdf' | 'video' | 'youtube';
}

const Library: React.FC<LibraryProps> = ({ themes, subThemes }) => {
  const [expandedThemes, setExpandedThemes] = useState<number[]>([]);
  const [expandedSubThemes, setExpandedSubThemes] = useState<number[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [documents, setDocuments] = useState<Record<string, Document[]>>({});

  useEffect(() => {
    const docs: Record<string, Document[]> = {};

    // Theme 1

    docs['better_understand_and_manage_myself/explore_my_personality'] = [
      {
        name: 'MBTI Workbook: Exploring Your Type',
        path: '/data/better_understand_and_manage_myself/explore_my_personality/Personality_MBTI_eBook_ExploringYourType.pdf',
        type: 'pdf'
      },
      {
        name: 'ZEST Video: MBTI Explained',
        path: 'whu9enRjTXg', // Only the video ID
        type: 'youtube'
      }
    ];
    // Theme 5
    docs['maximize_team_performance/motivate_the_team'] = [
      {
        name: 'MBTI Guide: Type and Learning Preferences',
        path: '/data/maximize_team_performance/motivate_the_team/TeamMotivation_MBTI_Guide_TypeAndLearningPreferences.pdf',
        type: 'pdf'
      },
      {
        name: 'ZEST Guide: Essentials of Leadership',
        path: '/data/maximize_team_performance/motivate_the_team/TeamMotivation_Lybaert_Guide_EssentialsOfLeadership.pdf',
        type: 'pdf'
      },
      {
        name: 'McClelland Guide: Identify Motivational Needs',
        path: '/data/maximize_team_performance/motivate_the_team/TeamMotivation_McCLelland_Guide_IdentifyMotivationalNeeds.pdf',
        type: 'pdf'
      }
    ];

    setDocuments(docs);
  }, []);

  const toggleTheme = (themeId: number) => {
    setExpandedThemes(prev =>
      prev.includes(themeId)
        ? prev.filter(id => id !== themeId)
        : [...prev, themeId]
    );
  };

  const toggleSubTheme = (subThemeId: number) => {
    setExpandedSubThemes(prev =>
      prev.includes(subThemeId)
        ? prev.filter(id => id !== subThemeId)
        : [...prev, subThemeId]
    );
  };

  const getThemePath = (theme: Theme, subTheme: SubTheme) => {
    const themeName = theme.title.toLowerCase().replace(/\s+/g, '_');
    const subThemeName = subTheme.title.toLowerCase().replace(/\s+/g, '_');
    return `${themeName}/${subThemeName}`;
  };

  const getDocuments = (theme: Theme, subTheme: SubTheme) => {
    const path = getThemePath(theme, subTheme);
    return documents[path] || [];
  };

  const handleDocumentClick = (doc: Document) => {
    setSelectedDocument(doc);
  };

  const closePreview = () => {
    setSelectedDocument(null);
  };

  const clearSearch = () => {
    setSearchQuery('');
  };

  const getSearchResults = () => {
    if (!searchQuery) return themes;

    const query = searchQuery.toLowerCase();
    const results: Theme[] = [];

    themes.forEach(theme => {
      const themeMatches = theme.title.toLowerCase().includes(query);
      const relevantSubThemes = subThemes.filter(st =>
        st.themeId === theme.id &&
        (st.title.toLowerCase().includes(query) ||
         getDocuments(theme, st).some(doc => doc.name.toLowerCase().includes(query)))
      );

      if (themeMatches || relevantSubThemes.length > 0) {
        results.push(theme);
      }
    });

    return results;
  };

  const searchResults = getSearchResults();

  const breadcrumbItems: BreadcrumbItem[] = [
    {
      label: 'Library',
      isActive: true
    }
  ];

  return (
    <div className="min-h-full flex flex-col">
      <div className="p-6 max-w-7xl mx-auto w-full relative">
        <Breadcrumbs
          items={breadcrumbItems}
          className="mb-6"
        />

        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 mb-1">Library</h1>
            <p className="text-gray-600">Access learning materials and resources</p>
          </div>

          <div className="w-64 relative">
            <div className="relative">
              <SearchIcon size={16} className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search resources..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-8 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
              {searchQuery && (
                <button
                  onClick={clearSearch}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <XIcon size={16} />
                </button>
              )}
            </div>
          </div>
        </div>

        <div className="flex gap-6 h-[calc(100vh-12rem)] relative">
          {/* Library Tree - Lower z-index */}
          <div className="w-1/3 bg-white rounded-lg border border-gray-200 overflow-y-auto relative z-0">
            <div className="p-4 space-y-1">
              {searchResults.map(theme => (
                <div key={theme.id} className="rounded-lg transition-colors">
                  <button
                    onClick={() => theme.isActive && toggleTheme(theme.id)}
                    className={`
                      w-full flex items-center px-3 py-2 rounded-lg transition-colors text-left
                      ${theme.isActive
                        ? 'hover:bg-gray-50 cursor-pointer'
                        : 'opacity-50 cursor-not-allowed'
                      }
                    `}
                  >
                    {theme.isActive ? (
                      expandedThemes.includes(theme.id)
                        ? <ChevronDownIcon size={16} className="text-gray-400 mr-2 flex-shrink-0" />
                        : <ChevronRightIcon size={16} className="text-gray-400 mr-2 flex-shrink-0" />
                    ) : (
                      <LockIcon size={16} className="text-gray-400 mr-2 flex-shrink-0" />
                    )}
                    <FolderIcon size={16} className="text-[#8866a7] mr-2 flex-shrink-0" />
                    <span className="text-sm font-medium text-gray-700 truncate">{theme.title}</span>
                  </button>

                  {(expandedThemes.includes(theme.id) || searchQuery) && theme.isActive && (
                    <div className="pl-8 space-y-1">
                      {subThemes
                        .filter(st => st.themeId === theme.id)
                        .filter(st =>
                          !searchQuery ||
                          st.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          getDocuments(theme, st).some(doc =>
                            doc.name.toLowerCase().includes(searchQuery.toLowerCase())
                          )
                        )
                        .map(subTheme => (
                          <div key={subTheme.id} className="rounded-lg transition-colors">
                            <button
                              onClick={() => subTheme.isActive && toggleSubTheme(subTheme.id)}
                              className={`
                                w-full flex items-center px-3 py-2 rounded-lg transition-colors text-left
                                ${subTheme.isActive
                                  ? 'hover:bg-gray-50 cursor-pointer'
                                  : 'opacity-50 cursor-not-allowed'
                                }
                              `}
                            >
                              {subTheme.isActive ? (
                                expandedSubThemes.includes(subTheme.id)
                                  ? <ChevronDownIcon size={16} className="text-gray-400 mr-2 flex-shrink-0" />
                                  : <ChevronRightIcon size={16} className="text-gray-400 mr-2 flex-shrink-0" />
                              ) : (
                                <LockIcon size={16} className="text-gray-400 mr-2 flex-shrink-0" />
                              )}
                              <FolderIcon size={16} className="text-gray-400 mr-2 flex-shrink-0" />
                              <span className="text-sm text-gray-600 truncate">{subTheme.title}</span>
                            </button>

                            {(expandedSubThemes.includes(subTheme.id) || searchQuery) && subTheme.isActive && (
                              <div className="pl-8 space-y-1">
                                {getDocuments(theme, subTheme)
                                  .filter(doc =>
                                    !searchQuery ||
                                    doc.name.toLowerCase().includes(searchQuery.toLowerCase())
                                  )
                                  .map((doc, index) => (
                                    <button
                                      key={index}
                                      onClick={() => handleDocumentClick(doc)}
                                      className={`
                                        w-full flex items-center px-3 py-2 rounded-lg transition-colors text-left
                                        ${selectedDocument?.path === doc.path ? 'bg-purple-50' : 'hover:bg-gray-50'}
                                      `}
                                    >
                                      {doc.type === 'pdf' ? (
                                        <FileTextIcon size={16} className="text-gray-400 mr-2 flex-shrink-0" />
                                      ) : (
                                        <PlayIcon size={16} className="text-gray-400 mr-2 flex-shrink-0" />
                                      )}
                                      <span className="text-sm text-gray-600 truncate">{doc.name}</span>
                                    </button>
                                  ))}
                                {getDocuments(theme, subTheme).length === 0 && !searchQuery && (
                                  <div className="px-3 py-2">
                                    <span className="text-xs text-gray-400">No documents available</span>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        ))}
                    </div>
                  )}
                </div>
              ))}
              {searchResults.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No results found
                </div>
              )}
            </div>
          </div>

          {/* Preview Panel - Higher z-index */}
          <div className="flex-1 bg-white rounded-lg border border-gray-200 overflow-hidden flex flex-col relative z-1">
            {selectedDocument ? (
              <>
                <div className="flex items-center justify-between p-4 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-800 truncate pr-4">
                    {selectedDocument.name}
                  </h2>
                  <button
                    onClick={closePreview}
                    className="text-gray-400 hover:text-gray-600 p-1 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <XIcon size={20} />
                  </button>
                </div>
                <div className="flex-1 bg-gray-50">
                  {selectedDocument.type === 'pdf' ? (
                  <iframe
                    src={selectedDocument.path}
                    className="w-full h-full"
                    title={selectedDocument.name}
                  />
                ) : selectedDocument.type === 'youtube' ? (
                  <iframe
                    src={`https://www.youtube.com/embed/${selectedDocument.path}`}
                    className="w-full h-full"
                    title={selectedDocument.name}
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowFullScreen
                  />
                ) : (
                  <video
                    src={selectedDocument.path}
                    controls
                    className="w-full h-full bg-black"
                  >
                    Your browser does not support the video tag.
                  </video>
                )}
                </div>
              </>
            ) : (
              <div className="h-full flex items-center justify-center text-gray-500">
                Select a document to preview
              </div>
            )}
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Library;
