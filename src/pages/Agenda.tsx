import React, { useState } from 'react';
import { Module } from '../types';
import { modules } from '../data';
import {
  MonitorIcon,
  UsersIcon,
  BookOpenIcon,
  ListIcon,
  CalendarIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  CheckIcon,
  LockIcon,
  ClockIcon,
  MapPinIcon
} from 'lucide-react';
import Footer from '../components/Footer';
import Breadcrumbs, { BreadcrumbItem } from '../components/Breadcrumbs';

interface AgendaProps {
  onOpenModule: (moduleId: string) => void;
}

const Agenda: React.FC<AgendaProps> = ({ onOpenModule }) => {
  const [viewMode, setViewMode] = useState<'list' | 'calendar'>('list');
  const [currentDate, setCurrentDate] = useState(new Date());

  const breadcrumbItems: BreadcrumbItem[] = [
    {
      label: 'Agenda',
      isActive: true
    }
  ];

  const getModuleIcon = (type: Module['type'], size: number = 16) => {
    switch (type) {
      case 'live_virtual':
        return <MonitorIcon size={size} className="text-green-500" />;
      case 'live_in_person':
        return <UsersIcon size={size} className="text-blue-500" />;
      case 'interactive':
        return <BookOpenIcon size={size} className="text-purple-500" />;
    }
  };

  const getModuleTypeLabel = (type: Module['type']) => {
    switch (type) {
      case 'live_in_person':
        return 'Live Session (in-person)';
      case 'live_virtual':
        return 'Live Session (virtual)';
      case 'interactive':
        return 'Assignment';
    }
  };

  const getModuleColor = (type: Module['type'], status: Module['status']) => {
    if (status === 'locked') return 'bg-gray-100 text-gray-500';
    switch (type) {
      case 'live_in_person':
        return 'bg-blue-50 text-blue-700 border-blue-200';
      case 'live_virtual':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'interactive':
        return 'bg-purple-50 text-purple-700 border-purple-200';
    }
  };

  const getStatusBadge = (status: Module['status']) => {
    switch (status) {
      case 'completed':
        return (
          <span className="bg-green-50 text-green-700 text-xs px-2 py-0.5 rounded-full flex items-center">
            <CheckIcon size={12} className="mr-1" />
            Completed
          </span>
        );
      case 'locked':
        return (
          <span className="bg-gray-50 text-gray-500 text-xs px-2 py-0.5 rounded-full flex items-center">
            <LockIcon size={12} className="mr-1" />
            Locked
          </span>
        );
      default:
        return (
          <span className="bg-blue-50 text-blue-700 text-xs px-2 py-0.5 rounded-full">
            Upcoming
          </span>
        );
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getModuleDueDate = (module: Module) => {
    if (module.type === 'interactive') {
      return new Date(module.date.getTime() + 7 * 24 * 60 * 60 * 1000);
    }
    return module.date;
  };

  const groupedModules = modules.reduce((acc, module) => {
    const displayDate = getModuleDueDate(module);
    const monthYear = displayDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    if (!acc[monthYear]) {
      acc[monthYear] = [];
    }
    acc[monthYear].push({ ...module, displayDate });
    return acc;
  }, {} as Record<string, (Module & { displayDate: Date })[]>);

  const navigate = (direction: 'prev' | 'next' | 'today') => {
    const newDate = new Date(currentDate);
    if (direction === 'today') {
      setCurrentDate(new Date());
    } else {
      newDate.setMonth(currentDate.getMonth() + (direction === 'next' ? 1 : -1));
      setCurrentDate(newDate);
    }
  };

  const getCalendarDays = () => {
    const days = [];
    const start = new Date(currentDate);
    start.setDate(1);
    const day = start.getDay();
    start.setDate(start.getDate() - (day === 0 ? 6 : day - 1));

    for (let i = 0; i < 42; i++) {
      days.push(new Date(start));
      start.setDate(start.getDate() + 1);
    }

    return days;
  };

  const getDayModules = (day: Date) => {
    return modules.map(module => {
      const displayDate = getModuleDueDate(module);
      return { ...module, displayDate };
    }).filter(module => {
      return module.displayDate.toDateString() === day.toDateString();
    });
  };

  const renderEventCard = (module: Module & { displayDate: Date }, isCompact: boolean = false) => {
    const colorClass = getModuleColor(module.type, module.status);
    const startTime = module.type === 'interactive'
      ? '9:00 AM'
      : formatTime(module.displayDate);

    const durationHours = module.type === 'interactive'
      ? 1
      : parseFloat(module.duration.split(' ')[0]);

    const endTime = new Date(module.displayDate);
    endTime.setHours(
      module.type === 'interactive'
        ? 10
        : endTime.getHours() + Math.floor(durationHours),
      module.type === 'interactive'
        ? 0
        : endTime.getMinutes() + ((durationHours % 1) * 60)
    );

    return (
      <button
        key={module.id}
        onClick={() => module.status !== 'locked' && onOpenModule(module.id)}
        className={`
          w-full text-left rounded border transition-all
          ${colorClass}
          ${module.status === 'locked' ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-sm cursor-pointer'}
          ${isCompact ? 'p-1 text-xs' : 'p-2 text-sm'}
        `}
      >
        <div className="flex items-center gap-1">
          {getModuleIcon(module.type)}
          <span className="font-medium truncate">{module.title}</span>
        </div>
        {!isCompact && (
          <>
            <div className="text-xs mt-1">
              {startTime} - {formatTime(endTime)}
            </div>
            <div className="mt-1">{getStatusBadge(module.status)}</div>
          </>
        )}
      </button>
    );
  };

  const renderListView = () => (
    <div className="space-y-8">
      {Object.entries(groupedModules).map(([monthYear, monthModules]) => (
        <div key={monthYear}>
          <h2 className="text-lg font-semibold text-gray-700 mb-4">{monthYear}</h2>
          <div className="space-y-4">
            {monthModules.map(module => (
              <div
                key={module.id}
                onClick={() => module.status !== 'locked' && onOpenModule(module.id)}
                className={`
                  bg-white rounded-lg border p-4 transition-all
                  ${module.status === 'locked'
                    ? 'opacity-50 cursor-not-allowed border-gray-200'
                    : 'hover:shadow-md cursor-pointer'
                  }
                `}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      {getModuleIcon(module.type)}
                      <span className="text-sm text-gray-600">
                        {getModuleTypeLabel(module.type)}
                      </span>
                      {getStatusBadge(module.status)}
                    </div>

                    <h3 className="text-lg font-medium text-gray-800 mb-1">
                      {module.title}
                    </h3>
                    <p className="text-sm text-gray-600 mb-3">
                      {module.description}
                    </p>

                    <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                      <span>{formatDate(module.displayDate)}</span>
                      <span>
                        {module.type === 'interactive' ? '9:00 AM · 1 hour' : `${formatTime(module.displayDate)} · ${module.duration}`}
                      </span>
                      {module.location && <span>{module.location}</span>}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );

  const renderCalendarView = () => {
    const days = getCalendarDays();
    const today = new Date();

    return (
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800">
              {currentDate.toLocaleString('en-US', {
                month: 'long',
                year: 'numeric'
              })}
            </h2>
            <div className="flex items-center gap-1">
              <button
                onClick={() => navigate('prev')}
                className="p-1 hover:bg-gray-100 rounded-full"
              >
                <ChevronLeftIcon size={18} />
              </button>
              <button
                onClick={() => navigate('today')}
                className="px-3 py-1 text-sm font-medium text-[#8866a7] hover:bg-purple-50 rounded"
              >
                Today
              </button>
              <button
                onClick={() => navigate('next')}
                className="p-1 hover:bg-gray-100 rounded-full"
              >
                <ChevronRightIcon size={18} />
              </button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-7 border-b">
          {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map(day => (
            <div key={day} className="p-2 text-center border-r last:border-r-0 text-sm font-medium text-gray-600">
              {day}
            </div>
          ))}
          {days.map(day => {
            const dayModules = getDayModules(day);
            const isCurrentMonth = day.getMonth() === currentDate.getMonth();
            const isToday = day.toDateString() === today.toDateString();

            return (
              <div
                key={day.toISOString()}
                className={`
                  min-h-[80px] p-1.5 border-r border-b last:border-r-0 transition-colors
                  ${isCurrentMonth ? 'bg-white' : 'bg-gray-50'}
                  ${isToday ? 'bg-purple-50 ring-2 ring-[#8866a7] ring-inset' : ''}
                `}
              >
                <span className={`
                  inline-flex items-center justify-center w-6 h-6 rounded-full
                  text-sm font-medium transition-colors
                  ${isCurrentMonth ? 'text-gray-900' : 'text-gray-400'}
                  ${isToday ? 'bg-[#8866a7] text-white' : ''}
                `}>
                  {day.getDate()}
                </span>
                <div className="mt-1 space-y-0.5">
                  {dayModules.slice(0, 2).map(module => renderEventCard(module, true))}
                  {dayModules.length > 2 && (
                    <div className="text-xs text-gray-500 pl-1">
                      +{dayModules.length - 2} more
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-full flex flex-col">
      <div className="p-6 max-w-7xl mx-auto w-full">
        <Breadcrumbs
          items={breadcrumbItems}
          className="mb-6"
        />

        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 mb-1">Agenda</h1>
            <p className="text-gray-600">Track your upcoming sessions and assignments</p>
          </div>

          <div className="flex items-center bg-white rounded-lg border border-gray-200 p-1">
            <button
              onClick={() => setViewMode('list')}
              className={`
                flex items-center px-3 py-1.5 rounded text-sm font-medium transition-colors
                ${viewMode === 'list'
                  ? 'bg-[#8866a7] text-white'
                  : 'text-gray-600 hover:text-gray-800'
                }
              `}
            >
              <ListIcon size={16} className="mr-1" />
              List View
            </button>
            <button
              onClick={() => setViewMode('calendar')}
              className={`
                flex items-center px-3 py-1.5 rounded text-sm font-medium transition-colors
                ${viewMode === 'calendar'
                  ? 'bg-[#8866a7] text-white'
                  : 'text-gray-600 hover:text-gray-800'
                }
              `}
            >
              <CalendarIcon size={16} className="mr-1" />
              Calendar View
            </button>
          </div>
        </div>

        {viewMode === 'list' ? renderListView() : renderCalendarView()}
      </div>
      <Footer />
    </div>
  );
};

export default Agenda;
