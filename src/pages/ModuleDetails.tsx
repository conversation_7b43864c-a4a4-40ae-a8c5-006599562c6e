import React from 'react';
import {
  ArrowLeftIcon,
  ClockIcon,
  MapPinIcon,
  CheckIcon,
  MonitorIcon,
  UsersIcon,
  BookOpenIcon,
  LockIcon
} from 'lucide-react';
import { Module } from '../types';
import { modules } from '../data';
import Footer from '../components/Footer';
import Breadcrumbs, { BreadcrumbItem } from '../components/Breadcrumbs';
import ErrorMessage from '../components/ErrorMessage';

interface ModuleDetailsProps {
  moduleId: string | null;
  navigationSource: 'dashboard' | 'agenda' | 'assignments';
  onBack: () => void;
}

const ModuleDetails: React.FC<ModuleDetailsProps> = ({ moduleId, navigationSource, onBack }) => {
  const getModuleIcon = (type: Module['type'], size: number = 16) => {
    switch (type) {
      case 'live_virtual':
        return <MonitorIcon size={size} className="text-green-500" />;
      case 'live_in_person':
        return <UsersIcon size={size} className="text-blue-500" />;
      case 'interactive':
        return <BookOpenIcon size={size} className="text-purple-500" />;
    }
  };

  const module = modules.find(m => m.id === moduleId);

  const getSourceLabel = () => {
    switch (navigationSource) {
      case 'agenda': return 'Agenda';
      case 'assignments': return 'Assignments';
      default: return 'Dashboard';
    }
  };

  const breadcrumbItems: BreadcrumbItem[] = [
    {
      label: module?.title || 'Module',
      isActive: true
    }
  ];

  if (!module) {
    return (
      <div className="min-h-full flex flex-col">
        <div className="p-6 max-w-7xl mx-auto w-full">
                    <Breadcrumbs
            items={[{ label: 'Module', isActive: true }]}
            className="mb-6"
          />

          <ErrorMessage
            title="Module Not Found"
            message="The requested module could not be found."
          />
        </div>
        <Footer />
      </div>
    );
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="min-h-full flex flex-col">
      <div className="p-6 max-w-7xl mx-auto w-full">
        <Breadcrumbs
          items={breadcrumbItems}
          className="mb-6"
        />

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="mb-6">
            <div className="flex items-center space-x-2 mb-2">
              {getModuleIcon(module.type)}
              <span className="text-sm text-gray-600">
                {module.type === 'live_in_person' ? 'Live Session (in-person)' :
                 module.type === 'live_virtual' ? 'Live Session (virtual)' :
                 'Interactive Module'}
              </span>
              <span className="text-gray-300">•</span>
              <span className={`
                inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                ${module.status === 'completed' ? 'bg-green-100 text-green-700' :
                  module.status === 'upcoming' ? 'bg-blue-100 text-blue-700' :
                  'bg-gray-100 text-gray-600'}
              `}>
                <CheckIcon size={12} className="mr-1" />
                {module.status.charAt(0).toUpperCase() + module.status.slice(1)}
              </span>
            </div>

            <h1 className="text-2xl font-bold text-gray-800 mb-2">{module.title}</h1>
            <p className="text-gray-600">{module.description}</p>
          </div>

          <div className="flex flex-wrap gap-4 mb-8 text-sm">
            <div className="flex items-center text-gray-600">
              <ClockIcon size={16} className="mr-2" />
              <div>
                <div>{formatDate(module.date)}</div>
                <div>{formatTime(module.date)} · {module.duration}</div>
              </div>
            </div>
            {module.location && (
              <div className="flex items-center text-gray-600">
                <MapPinIcon size={16} className="mr-2" />
                <span>{module.location}</span>
              </div>
            )}
          </div>

          {module.content && (
            <div className="space-y-6">
              {module.content.objectives && (
                <div>
                  <h2 className="text-lg font-semibold text-gray-800 mb-3">Learning Objectives</h2>
                  <ul className="list-disc pl-5 space-y-2 text-gray-600">
                    {module.content.objectives.map((objective, index) => (
                      <li key={index}>{objective}</li>
                    ))}
                  </ul>
                </div>
              )}

              {module.content.agenda && (
                <div>
                  <h2 className="text-lg font-semibold text-gray-800 mb-3">Agenda</h2>
                  <ol className="list-decimal pl-5 space-y-2 text-gray-600">
                    {module.content.agenda.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ol>
                </div>
              )}

              {module.content.prerequisites && (
                <div>
                  <h2 className="text-lg font-semibold text-gray-800 mb-3">Prerequisites</h2>
                  <ul className="list-disc pl-5 space-y-2 text-gray-600">
                    {module.content.prerequisites.map((prerequisite, index) => (
                      <li key={index}>{prerequisite}</li>
                    ))}
                  </ul>
                </div>
              )}

              {module.content.materials && (
                <div>
                  <h2 className="text-lg font-semibold text-gray-800 mb-3">Materials</h2>
                  <ul className="list-disc pl-5 space-y-2 text-gray-600">
                    {module.content.materials.map((material, index) => (
                      <li key={index}>{material}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default ModuleDetails;
