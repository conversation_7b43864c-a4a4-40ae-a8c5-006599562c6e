import React from 'react';
import { useConversations } from '../contexts/ConversationContext';
import { MessageCircleIcon, ClockIcon } from 'lucide-react';
import Footer from '../components/Footer';
import Breadcrumbs, { BreadcrumbItem } from '../components/Breadcrumbs';

interface ChatHistoryProps {
  onSelectConversation: (conversationId: string) => void;
}

const ChatHistory: React.FC<ChatHistoryProps> = ({ onSelectConversation }) => {
  const { conversations } = useConversations();

  const breadcrumbItems: BreadcrumbItem[] = [
    {
      label: 'Chat History',
      isActive: true
    }
  ];

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="min-h-full flex flex-col">
      <div className="p-6 max-w-7xl mx-auto w-full">
        <Breadcrumbs
          items={breadcrumbItems}
          className="mb-6"
        />

        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-1">Chat History</h1>
          <p className="text-gray-600">Review your previous conversations and insights</p>
        </div>

        <div className="space-y-4">
          {conversations.length === 0 ? (
            <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
              <MessageCircleIcon size={40} className="text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">No conversations yet</p>
              <p className="text-sm text-gray-400 mt-1">Start chatting with Zest to see your conversation history here</p>
            </div>
          ) : (
            conversations.map(conversation => (
              <div
                key={conversation.id}
                onClick={() => onSelectConversation(conversation.id)}
                className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-gray-900 truncate">
                      {conversation.title}
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">
                      {conversation.theme}
                    </p>
                    {conversation.messages.length > 1 && (
                      <p className="text-sm text-gray-600 mt-2 line-clamp-2">
                        {conversation.messages[conversation.messages.length - 1].content.substring(0, 100)}...
                      </p>
                    )}
                  </div>
                  <div className="flex items-center text-xs text-gray-400 ml-4">
                    <ClockIcon size={14} className="mr-1" />
                    {formatDate(conversation.timestamp)}
                  </div>
                </div>
                <div className="mt-3 flex items-center justify-between text-xs text-gray-500">
                  <span>{conversation.messages.length} messages</span>
                  <span>Last updated {formatDate(conversation.timestamp)}</span>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default ChatHistory;
