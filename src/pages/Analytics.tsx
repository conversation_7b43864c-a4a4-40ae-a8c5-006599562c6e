import React from 'react';
import { BarChart2Icon, TrendingUpIcon, ClockIcon, BookOpenIcon } from 'lucide-react';
import Footer from '../components/Footer';
import { useAnalytics } from '../hooks/useAnalytics';
import Breadcrumbs, { BreadcrumbItem } from '../components/Breadcrumbs';

const Analytics: React.FC = () => {
  const kpiData = useAnalytics();

  const breadcrumbItems: BreadcrumbItem[] = [
    {
      label: 'Analytics',
      isActive: true
    }
  ];

  const stats = [
    {
      name: 'Interactions',
      value: kpiData.interactions,
      icon: BarChart2Icon,
      description: 'Messages sent by you',
      color: 'text-green-500',
      bgColor: 'bg-green-50'
    },
    {
      name: 'Time Spent',
      value: kpiData.timeSpent,
      icon: ClockIcon,
      description: 'Total time on platform',
      color: 'text-purple-500',
      bgColor: 'bg-purple-50'
    },
    {
      name: 'Satisfaction',
      value: `${kpiData.satisfaction}/5`,
      icon: TrendingUpIcon,
      description: 'Average satisfaction score',
      color: 'text-blue-500',
      bgColor: 'bg-blue-50'
    },
    {
      name: 'Themes Used',
      value: kpiData.themesUsage.length,
      icon: BookOpenIcon,
      description: 'Number of themes explored',
      color: 'text-orange-500',
      bgColor: 'bg-orange-50'
    }
  ];

  return (
    <div className="min-h-full flex flex-col">
      <div className="p-6 max-w-7xl mx-auto w-full">
        <Breadcrumbs
          items={breadcrumbItems}
          className="mb-6"
        />

        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-1">Analytics</h1>
          <p className="text-gray-600">Track your learning progress and engagement</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat) => {
            const Icon = stat.icon;
            return (
              <div
                key={stat.name}
                className="bg-white rounded-lg border border-gray-200 p-6"
              >
                <div className="flex items-center">
                  <div className={`${stat.bgColor} p-3 rounded-lg`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
                <div className="mt-4">
                  <h3 className="text-sm font-medium text-gray-500">{stat.name}</h3>
                  <div className="mt-1">
                    <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                    <p className="text-sm text-gray-500 mt-1">{stat.description}</p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Graphique de l'utilisation des thèmes */}
        <div className="mt-8 bg-white rounded-lg border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-6">Theme Usage Distribution</h2>
          <div className="space-y-6">
            {kpiData.themesUsage.map((theme, index) => (
              <div key={index} className="flex items-center">
                <div className="w-48 text-sm text-gray-600">{theme.theme}</div>
                <div className="flex-1">
                  <div className="h-4 rounded-full bg-gray-100 overflow-hidden">
                    <div
                      className="h-full rounded-full"
                      style={{
                        width: `${theme.percentage}%`,
                        backgroundColor: theme.color
                      }}
                    />
                  </div>
                </div>
                <div className="w-16 text-right text-sm text-gray-600">
                  {theme.percentage}%
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Analytics;
