import React from 'react';
import { Theme, Module } from '../types';
import { user, modules } from '../data';
import {
  MonitorIcon,
  UsersIcon,
  BookOpenIcon,
  ClockIcon,
  MapPinIcon,
  MessageSquareIcon,
  CalendarIcon,
  CheckSquareIcon
} from 'lucide-react';
import Footer from '../components/Footer';

interface DashboardProps {
  themes: Theme[];
  onSelectTheme: (themeId: number) => void;
  onOpenModule: (moduleId: string) => void;
}

const Dashboard: React.FC<DashboardProps> = ({ themes, onSelectTheme, onOpenModule }) => {
  const getModuleIcon = (type: Module['type'], size: number = 16) => {
    switch (type) {
      case 'live_virtual':
        return <MonitorIcon size={size} className="text-green-500" />;
      case 'live_in_person':
        return <UsersIcon size={size} className="text-blue-500" />;
      case 'interactive':
        return <BookOpenIcon size={size} className="text-purple-500" />;
    }
  };

  const upcomingModules = modules
    .filter(m => (m.type === 'live_virtual' || m.type === 'live_in_person') && m.status !== 'locked')
    .sort((a, b) => a.date.getTime() - b.date.getTime())
    .slice(0, 3);

  const assignments = modules
    .filter(m => m.type === 'interactive' && m.status !== 'locked')
    .sort((a, b) => a.date.getTime() - b.date.getTime());

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-full flex flex-col">
      <div className="p-6 max-w-7xl mx-auto w-full">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Left Section (66%) */}
          <div className="lg:w-2/3">
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-gray-800 mb-1">
                Welcome{user?.firstName ? `, ${user.firstName}` : ''}
              </h1>
              <p className="text-gray-600 font-medium">
                How can I help you today?
              </p>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <MessageSquareIcon size={20} className="mr-2 text-gray-500" />
                Start a Conversation
              </h2>
              <div className="grid md:grid-cols-2 gap-4">
                {themes.map(theme => (
                    <div
                      key={theme.id}
                      className={`
                        rounded-lg p-4 transition-all duration-300 border
                        ${theme.isActive
                          ? 'bg-white border-gray-200 hover:shadow-md cursor-pointer'
                          : 'bg-gray-50 border-gray-100 cursor-not-allowed'
                        }
                      `}
                      onClick={() => theme.isActive && onSelectTheme(theme.id)}
                      role={theme.isActive ? 'button' : 'presentation'}
                      aria-disabled={!theme.isActive}
                      style={{
                        borderTop: theme.isActive ? `4px solid ${theme.color}` : '4px solid transparent'
                      }}
                    >
                      <div className="flex flex-col h-full">
                        <h3 className={`text-lg font-semibold mb-2 ${!theme.isActive && 'text-gray-400'}`}>
                          {theme.title}
                        </h3>
                        <p className={`text-sm mb-3 flex-grow ${theme.isActive ? 'text-gray-600' : 'text-gray-400'}`}>
                          {theme.description}
                        </p>
                        {theme.isActive ? (
                          <button
                            className="flex items-center text-gray-700 text-sm font-medium hover:underline transition-colors"
                            aria-label={`Explore ${theme.title}`}
                          >
                            Explore
                          </button>
                        ) : (
                          <div className="flex items-center text-gray-400 text-sm">
                            Locked
                          </div>
                        )}
                      </div>
                    </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Section (33%) */}
          <div className="lg:w-1/3 lg:pt-[88px]">
            <div className="lg:sticky lg:top-6 space-y-6">
              {/* Calendar Component */}
              <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
                <h2 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <CalendarIcon size={20} className="mr-2 text-gray-500" />
                  Upcoming Events
                </h2>
                <div className="space-y-3">
                  {upcomingModules.map(module => (
                    <button
                      key={module.id}
                      onClick={() => onOpenModule(module.id)}
                      className="w-full group"
                    >
                      <div className="flex items-start space-x-3 hover:bg-gray-50 p-2 rounded-lg transition-colors">
                        <div className="flex-shrink-0 w-10 h-10 bg-purple-50 rounded-lg flex items-center justify-center">
                          {getModuleIcon(module.type, 14)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium text-gray-800 group-hover:text-[#8866a7] transition-colors text-left truncate text-sm">
                            {module.title}
                          </h3>
                          <div className="text-xs text-gray-500 space-y-1 mt-1">
                            <div className="flex items-center">
                              <ClockIcon size={12} className="mr-1 flex-shrink-0" />
                              {formatDate(module.date)}
                            </div>
                            {module.location && (
                              <div className="flex items-center">
                                <MapPinIcon size={12} className="mr-1 flex-shrink-0" />
                                {module.location}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Assignments Block */}
              <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
                <h2 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <CheckSquareIcon size={20} className="mr-2 text-gray-500" />
                  Assignments
                </h2>
                <div className="space-y-3">
                  {assignments.map(module => (
                    <button
                      key={module.id}
                      onClick={() => onOpenModule(module.id)}
                      className="w-full group"
                    >
                      <div className="space-y-2 hover:bg-gray-50 p-2 rounded-lg transition-colors">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium text-gray-800 group-hover:text-[#8866a7] transition-colors text-left truncate text-sm">
                            {module.title}
                          </h3>
                          <span className="text-xs text-gray-500 flex-shrink-0 ml-2">
                            Due {formatDate(new Date(module.date.getTime() + 7 * 24 * 60 * 60 * 1000))}
                          </span>
                        </div>
                        <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-[#8866a7] rounded-full transition-all duration-300"
                            style={{ width: module.status === 'completed' ? '100%' : '0%' }}
                            role="progressbar"
                            aria-valuenow={module.status === 'completed' ? 100 : 0}
                            aria-valuemin={0}
                            aria-valuemax={100}
                          />
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Dashboard;
