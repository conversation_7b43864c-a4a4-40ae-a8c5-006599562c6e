import React, { useState, useEffect } from 'react';
import { ArrowLeftIcon, DownloadIcon } from 'lucide-react';
import { caseStudies, modules } from '../data';
import { CaseStudyQuestion } from '../types';
import { jsPDF } from 'jspdf';
import Footer from '../components/Footer';
import Breadcrumbs, { BreadcrumbItem } from '../components/Breadcrumbs';
import ErrorMessage from '../components/ErrorMessage';

interface CaseStudyPageProps {
  caseStudyId: string | null;
  navigationSource: 'dashboard' | 'agenda' | 'assignments';
  onBack: () => void;
}

const CaseStudyPage: React.FC<CaseStudyPageProps> = ({ caseStudyId, navigationSource, onBack }) => {
  const [questions, setQuestions] = useState<CaseStudyQuestion[]>([]);
  const [hasAnswers, setHasAnswers] = useState(false);
  const [lastSaved, setLastSaved] = useState<string | null>(null);

  const caseStudy = caseStudies.find(cs => cs.id === caseStudyId);
  const module = modules.find(m => m.content?.caseStudyId === caseStudyId);

  const dueDate = module ? new Date(module.date.getTime() + 7 * 24 * 60 * 60 * 1000) : null;

  const getSourceLabel = () => {
    switch (navigationSource) {
      case 'agenda': return 'Agenda';
      case 'assignments': return 'Assignments';
      default: return 'Dashboard';
    }
  };

  const breadcrumbItems: BreadcrumbItem[] = [
    {
      label: caseStudy?.title || 'Case Study',
      isActive: true
    }
  ];

  if (!caseStudy) {
    return (
      <div className="min-h-full flex flex-col">
        <div className="p-6 max-w-7xl mx-auto w-full">
                    <Breadcrumbs
            items={[{ label: 'Case Study', isActive: true }]}
            className="mb-6"
          />

          <ErrorMessage
            title="Case Study Not Found"
            message="The requested case study could not be found."
          />
        </div>
        <Footer />
      </div>
    );
  }

  useEffect(() => {
    if (caseStudy) {
      const storageKey = `caseStudy-${caseStudy.id}`;
      const savedData = localStorage.getItem(storageKey);

      if (savedData) {
        try {
          const parsedData = JSON.parse(savedData);

          if (parsedData.answers && parsedData.timestamp) {
            setQuestions(parsedData.answers);
            setLastSaved(new Date(parsedData.timestamp).toLocaleString());
          } else if (Array.isArray(parsedData)) {
            setQuestions(parsedData);
          } else {
            setQuestions(caseStudy.questions);
          }

          const hasAtLeastOneAnswer = Array.isArray(parsedData.answers || parsedData) &&
            (parsedData.answers || parsedData).some((q: CaseStudyQuestion) => q.answer.trim() !== '');
          setHasAnswers(hasAtLeastOneAnswer);
        } catch (e) {
          console.error("Error parsing saved case study data:", e);
          setQuestions(caseStudy.questions);
        }
      } else {
        setQuestions(caseStudy.questions);
      }
    }
  }, [caseStudy]);

  useEffect(() => {
    if (caseStudy && questions.length > 0) {
      const hasAtLeastOneAnswer = questions.some(q => q.answer.trim() !== '');
      setHasAnswers(hasAtLeastOneAnswer);

      const now = new Date();
      const storageData = {
        answers: questions,
        timestamp: now.toISOString()
      };

      localStorage.setItem(`caseStudy-${caseStudy.id}`, JSON.stringify(storageData));
      setLastSaved(now.toLocaleString());
    }
  }, [questions, caseStudy]);

  const handleAnswerChange = (id: string, answer: string) => {
    setQuestions(prevQuestions =>
      prevQuestions.map(q =>
        q.id === id ? { ...q, answer } : q
      )
    );
  };

  const exportToPDF = () => {
    if (!caseStudy) return;

    const doc = new jsPDF();

    doc.setFontSize(18);
    doc.text(caseStudy.title, 20, 20);

    doc.setFontSize(10);
    const timestamp = new Date().toLocaleString();
    doc.text(`Generated on: ${timestamp}`, 20, 30);

    doc.setFontSize(12);
    doc.text("Context:", 20, 40);

    const contextLines = doc.splitTextToSize(caseStudy.context, 170);
    doc.text(contextLines, 20, 45);

    let yPosition = 45 + contextLines.length * 7;

    questions.forEach((q, index) => {
      doc.setFontSize(12);
      doc.setFont('helvetica', 'bold');
      const questionText = `Q${index + 1}: ${q.question}`;
      const questionLines = doc.splitTextToSize(questionText, 170);
      doc.text(questionLines, 20, yPosition);

      yPosition += questionLines.length * 7;

      doc.setFont('helvetica', 'normal');
      if (q.answer.trim()) {
        const answerLines = doc.splitTextToSize(q.answer, 170);
        doc.text(answerLines, 20, yPosition);
        yPosition += answerLines.length * 7 + 10;
      } else {
        doc.text("No answer provided", 20, yPosition);
        yPosition += 17;
      }

      if (yPosition > 270) {
        doc.addPage();
        yPosition = 20;
      }
    });

    doc.save(`${caseStudy?.id || 'case-study'}-answers.pdf`);
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-full flex flex-col">
      <div className="p-6 max-w-7xl mx-auto w-full">
        <Breadcrumbs
          items={breadcrumbItems}
          className="mb-6"
        />

        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">{caseStudy.title}</h2>
          <p className="text-sm text-gray-500 mb-4">{caseStudy.description}</p>

          {dueDate && (
            <div className="bg-blue-50 text-blue-700 px-4 py-2 rounded-md mb-4">
              Due: {formatDate(dueDate)}
            </div>
          )}

          <div className="bg-purple-50 p-5 rounded-lg mb-6">
            <h3 className="font-medium text-purple-800 mb-3">Context</h3>
            <p className="text-sm whitespace-pre-line">{caseStudy.context}</p>
          </div>

          <div className="bg-gray-50 p-5 rounded-lg mb-6">
            <h3 className="font-medium text-gray-800 mb-2">About This Assignment</h3>
            <p className="text-sm text-gray-600 mb-3">
              This assignment is designed to help you deepen the insights gained during the kick-off session. By applying your MBTI profile to a practical scenario, you'll begin connecting who you are with how you lead.

              </p>
              <p className="text-sm text-gray-600 mb-3">
              This reflection also prepares you for the upcoming live session on Leadership & Self-Alignment. The commitments you articulate here will serve as a foundation for the work ahead.

            </p>
            <p className="text-sm text-gray-600">
              You can answer the questions at your own pace. Your responses are saved automatically, and you can revisit them anytime before the due date to revise or complete your input. Once finished, you'll have the option to export or share your reflections with your facilitator.
            </p>
          </div>

          <div className="space-y-8">
            {questions.map((q, index) => (
              <div key={q.id} className="border-b border-gray-100 pb-6">
                <h4 className="font-medium text-gray-800 mb-3">
                  Question {index + 1}: {q.question}
                </h4>
                <textarea
                  value={q.answer}
                  onChange={(e) => handleAnswerChange(q.id, e.target.value)}
                  placeholder="Type your answer here..."
                  className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent min-h-[120px] text-sm"
                />
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              {lastSaved && (
                <p className="text-xs text-gray-500">Last saved: {lastSaved}</p>
              )}
            </div>

            {hasAnswers && (
              <button
                onClick={exportToPDF}
                className="flex items-center bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-md transition-colors"
              >
                <DownloadIcon size={16} className="mr-2" />
                Export as PDF
              </button>
            )}
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default CaseStudyPage;
