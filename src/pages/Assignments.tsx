import React, { useState, useEffect } from 'react';
import {
  FileTextIcon,
  DownloadIcon,
  ClockIcon,
  CheckCircleIcon,
  AlertCircleIcon,
  Calendar,
  User
} from 'lucide-react';
import { jsPDF } from 'jspdf';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import { AssignmentWithDetails, UserAssignment, AssignmentResponse, AssignmentQuestion } from '../types';
import Footer from '../components/Footer';
import Breadcrumbs, { BreadcrumbItem } from '../components/Breadcrumbs';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

interface AssignmentsProps {}

const Assignments: React.FC<AssignmentsProps> = () => {
  const { profile } = useAuth();
  const [assignments, setAssignments] = useState<AssignmentWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedAssignment, setSelectedAssignment] = useState<AssignmentWithDetails | null>(null);
  const [responses, setResponses] = useState<Record<string, string>>({});
  const [saving, setSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<string | null>(null);

  const breadcrumbItems: BreadcrumbItem[] = selectedAssignment ? [
    {
      label: 'Assignments',
      onClick: () => setSelectedAssignment(null)
    },
    {
      label: selectedAssignment.title,
      isActive: true
    }
  ] : [
    {
      label: 'Assignments',
      isActive: true
    }
  ];

  useEffect(() => {
    if (profile) {
      loadUserAssignments();
    }
  }, [profile]);

  const loadUserAssignments = async () => {
    try {
      setLoading(true);

      // Charger les assignements de l'utilisateur
      const { data, error } = await supabase
        .from('user_assignments')
        .select(`
          *,
          assignments (
            *,
            assignment_questions (*)
          )
        `)
        .eq('user_id', profile?.id)
        .order('assigned_at', { ascending: false });

      if (error) {
        console.error('Erreur lors du chargement des assignements:', error);
        return;
      }

      // Transformer les données pour correspondre à notre interface
      const assignmentsWithDetails = data?.map((userAssignment: any) => ({
        ...userAssignment.assignments,
        questions: userAssignment.assignments.assignment_questions,
        user_assignment: userAssignment
      })) || [];

      setAssignments(assignmentsWithDetails);

      // Charger les réponses existantes pour tous les assignements
      if (assignmentsWithDetails.length > 0) {
        const userAssignmentIds = assignmentsWithDetails.map(a => a.user_assignment.id);
        const { data: responsesData } = await supabase
          .from('assignment_responses')
          .select('*')
          .in('user_assignment_id', userAssignmentIds);

        if (responsesData) {
          const responseMap: Record<string, string> = {};
          responsesData.forEach((response: AssignmentResponse) => {
            responseMap[response.question_id] = response.answer;
          });
          setResponses(responseMap);
        }
      }
    } catch (error) {
      console.error('Erreur:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveResponse = async (questionId: string, answer: string) => {
    if (!selectedAssignment?.user_assignment) return;

    try {
      setSaving(true);

      const { error } = await supabase
        .from('assignment_responses')
        .upsert({
          user_assignment_id: selectedAssignment.user_assignment.id,
          question_id: questionId,
          answer: answer
        });

      if (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        return;
      }

      setLastSaved(new Date().toLocaleString('fr-FR'));

      // Mettre à jour le statut de l'assignement si c'est la première réponse
      if (selectedAssignment.user_assignment.status === 'assigned') {
        await supabase
          .from('user_assignments')
          .update({ status: 'in_progress' })
          .eq('id', selectedAssignment.user_assignment.id);
      }
    } catch (error) {
      console.error('Erreur:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleAnswerChange = (questionId: string, answer: string) => {
    setResponses(prev => ({
      ...prev,
      [questionId]: answer
    }));

    // Debounce la sauvegarde
    clearTimeout((window as any).saveTimeout);
    (window as any).saveTimeout = setTimeout(() => {
      saveResponse(questionId, answer);
    }, 1000);
  };

  const markAsCompleted = async () => {
    if (!selectedAssignment?.user_assignment) return;

    try {
      const { error } = await supabase
        .from('user_assignments')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString()
        })
        .eq('id', selectedAssignment.user_assignment.id);

      if (error) {
        console.error('Erreur lors du marquage comme terminé:', error);
        return;
      }

      // Recharger les assignements
      loadUserAssignments();
      setSelectedAssignment(null);
    } catch (error) {
      console.error('Erreur:', error);
    }
  };

  const exportToPDF = () => {
    if (!selectedAssignment) return;

    const doc = new jsPDF();

    // En-tête
    doc.setFontSize(18);
    doc.text(selectedAssignment.title, 20, 20);

    doc.setFontSize(10);
    const timestamp = new Date().toLocaleString('fr-FR');
    doc.text(`Généré le : ${timestamp}`, 20, 30);

    if (profile) {
      doc.text(`Par : ${profile.first_name} ${profile.last_name}`, 20, 35);
    }

    // Description
    doc.setFontSize(12);
    doc.text("Description:", 20, 45);
    const descriptionLines = doc.splitTextToSize(selectedAssignment.description, 170);
    doc.text(descriptionLines, 20, 50);

    let yPosition = 50 + descriptionLines.length * 7 + 10;

    // Contexte
    doc.text("Contexte:", 20, yPosition);
    yPosition += 5;
    const contextLines = doc.splitTextToSize(selectedAssignment.context, 170);
    doc.text(contextLines, 20, yPosition);

    yPosition += contextLines.length * 7 + 15;

    // Questions et réponses
    selectedAssignment.questions?.forEach((question: AssignmentQuestion, index) => {
      doc.setFont('helvetica', 'bold');
      const questionText = `Q${index + 1}: ${question.question}`;
      const questionLines = doc.splitTextToSize(questionText, 170);
      doc.text(questionLines, 20, yPosition);

      yPosition += questionLines.length * 7;

      doc.setFont('helvetica', 'normal');
      const answer = responses[question.id] || 'Aucune réponse fournie';
      const answerLines = doc.splitTextToSize(answer, 170);
      doc.text(answerLines, 20, yPosition);
      yPosition += answerLines.length * 7 + 10;

      if (yPosition > 270) {
        doc.addPage();
        yPosition = 20;
      }
    });

    doc.save(`${selectedAssignment.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_reponses.pdf`);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return (
          <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircleIcon className="w-3 h-3" />
            Terminé
          </span>
        );
      case 'in_progress':
        return (
          <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <ClockIcon className="w-3 h-3" />
            En cours
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
            <AlertCircleIcon className="w-3 h-3" />
            À faire
          </span>
        );
    }
  };

  const formatDate = (date: string) => {
    return format(new Date(date), 'dd/MM/yyyy', { locale: fr });
  };

  if (loading) {
    return (
      <div className="min-h-full flex flex-col">
        <div className="p-6 max-w-7xl mx-auto w-full">
          <Breadcrumbs items={breadcrumbItems} className="mb-6" />
          <LoadingSpinner />
        </div>
        <Footer />
      </div>
    );
  }

  // Vue détaillée d'un assignement
  if (selectedAssignment) {
    const hasAnswers = selectedAssignment.questions?.some((q: AssignmentQuestion) =>
      responses[q.id]?.trim() !== ''
    );

    return (
      <div className="min-h-full flex flex-col">
        <div className="p-6 max-w-7xl mx-auto w-full">
          <Breadcrumbs items={breadcrumbItems} className="mb-6" />

          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">{selectedAssignment.title}</h2>
                <p className="text-sm text-gray-500 mb-4">{selectedAssignment.description}</p>
                {getStatusBadge(selectedAssignment.user_assignment?.status || 'assigned')}
              </div>
              <div className="flex items-center gap-2">
                {hasAnswers && (
                  <button
                    onClick={exportToPDF}
                    className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    <DownloadIcon className="w-4 h-4" />
                    Exporter PDF
                  </button>
                )}
                {selectedAssignment.user_assignment?.status !== 'completed' && hasAnswers && (
                  <button
                    onClick={markAsCompleted}
                    className="inline-flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                  >
                    <CheckCircleIcon className="w-4 h-4" />
                    Marquer comme terminé
                  </button>
                )}
              </div>
            </div>

            {selectedAssignment.user_assignment?.due_date && (
              <div className="bg-blue-50 text-blue-700 px-4 py-2 rounded-md mb-4">
                À rendre avant le : {formatDate(selectedAssignment.user_assignment.due_date)}
              </div>
            )}

            <div className="bg-purple-50 p-5 rounded-lg mb-6">
              <h3 className="font-medium text-purple-800 mb-3">Contexte</h3>
              <p className="text-sm whitespace-pre-line text-purple-700">{selectedAssignment.context}</p>
            </div>

            {selectedAssignment.instructions && (
              <div className="bg-blue-50 p-5 rounded-lg mb-6">
                <h3 className="font-medium text-blue-800 mb-3">Instructions</h3>
                <p className="text-sm whitespace-pre-line text-blue-700">{selectedAssignment.instructions}</p>
              </div>
            )}

            {lastSaved && (
              <div className="mb-4 text-sm text-gray-500">
                Dernière sauvegarde : {lastSaved}
                {saving && <span className="ml-2 text-blue-600">Sauvegarde en cours...</span>}
              </div>
            )}

            <div className="space-y-6">
              {selectedAssignment.questions?.map((question: AssignmentQuestion, index) => (
                <div key={question.id} className="border border-gray-200 rounded-lg p-5">
                  <div className="mb-4">
                    <h4 className="font-medium text-gray-900 mb-2">
                      Question {index + 1}
                    </h4>
                    <p className="text-gray-700">{question.question}</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Votre réponse
                    </label>
                    <textarea
                      value={responses[question.id] || ''}
                      onChange={(e) => handleAnswerChange(question.id, e.target.value)}
                      rows={6}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                      placeholder="Saisissez votre réponse ici..."
                      disabled={selectedAssignment.user_assignment?.status === 'completed'}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  // Vue liste des assignements
  return (
    <div className="min-h-full flex flex-col">
      <div className="p-6 max-w-7xl mx-auto w-full">
        <Breadcrumbs items={breadcrumbItems} className="mb-6" />

        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-1">Assignements</h1>
          <p className="text-gray-600">Vos exercices et travaux à compléter</p>
        </div>

        <div className="space-y-4">
          {assignments.length === 0 ? (
            <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
              <FileTextIcon size={40} className="text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">Aucun assignement pour le moment</p>
            </div>
          ) : (
            assignments.map(assignment => (
              <div
                key={assignment.id}
                onClick={() => setSelectedAssignment(assignment)}
                className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">{assignment.title}</h3>
                    <p className="text-gray-600 mb-2">{assignment.description}</p>
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <span className="flex items-center gap-1">
                        <FileTextIcon className="w-4 h-4" />
                        {assignment.questions?.length || 0} questions
                      </span>
                      <span className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        Assigné le {formatDate(assignment.user_assignment?.assigned_at || assignment.created_at)}
                      </span>
                      {assignment.user_assignment?.due_date && (
                        <span className="flex items-center gap-1">
                          <ClockIcon className="w-4 h-4" />
                          À rendre le {formatDate(assignment.user_assignment.due_date)}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex flex-col items-end gap-2">
                    {getStatusBadge(assignment.user_assignment?.status || 'assigned')}
                    {assignment.user_assignment?.completed_at && (
                      <span className="text-xs text-gray-500">
                        Terminé le {formatDate(assignment.user_assignment.completed_at)}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Assignments;
