import React, { useState, useEffect, useRef } from 'react';
import { ChatMessage as ChatMessageType, SubTheme, Theme } from '../types';
import ChatMessage from '../components/ChatMessage';
import ChatInput from '../components/ChatInput';
import { XIcon } from 'lucide-react';
import { generateRAGResponse } from '../utils/rag';
import { useConversations } from '../contexts/ConversationContext';

interface ChatInterfaceProps {
  theme: Theme;
  subTheme: SubTheme;
  onClose: () => void;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  theme,
  subTheme,
  onClose
}) => {
  const [messages, setMessages] = useState<ChatMessageType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { addConversation, addMessageToConversation, conversations } = useConversations();
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);

  useEffect(() => {
    // Chercher une conversation existante pour ce thème et sous-thème
    const existingConversation = conversations.find(
      conv => conv.theme === theme.title && conv.title === subTheme.title
    );

    if (existingConversation) {
      // Si une conversation existe, charger ses messages
      setMessages(existingConversation.messages);
      setCurrentConversationId(existingConversation.id);
    } else {
      // Créer une nouvelle conversation avec le message de bienvenue
      const welcomeMessage: ChatMessageType = {
        id: Date.now().toString(),
        sender: 'assistant',
        content: `Welcome to the "**${subTheme.title}**" discussion. How can I help with your leadership challenge today?`,
        timestamp: new Date()
      };

      const newConversation = addConversation(theme.title, subTheme.title, welcomeMessage);
      setCurrentConversationId(newConversation.id);
      setMessages([welcomeMessage]);
    }
  }, [theme.title, subTheme.title, addConversation, conversations]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = async (content: string) => {
    if (!currentConversationId) return;

    const userMessage: ChatMessageType = {
      id: Date.now().toString(),
      sender: 'user',
      content,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // Ajouter le message de l'utilisateur à la conversation
    addMessageToConversation(currentConversationId, userMessage);

    try {
      const response = await generateRAGResponse(content, theme.title, subTheme);

      const assistantMessage: ChatMessageType = {
        id: (Date.now() + 1).toString(),
        sender: 'assistant',
        content: response,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);
      addMessageToConversation(currentConversationId, assistantMessage);

    } catch (error) {
      const errorMessage: ChatMessageType = {
        id: (Date.now() + 1).toString(),
        sender: 'assistant',
        content: "I apologize, but I encountered an error. Please try again or contact support if the issue persists.",
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
      addMessageToConversation(currentConversationId, errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div className="sticky top-0 z-10 h-16 border-b border-gray-200 bg-white">
        <div className="h-full px-6 flex items-center justify-between">
          <div>
            <h1 className="font-medium text-gray-800">{subTheme.title}</h1>
            <p className="text-sm text-gray-600">{theme.title}</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XIcon size={20} />
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto bg-gray-50">
        <div className="px-6 py-4">
          {messages.map(message => (
            <ChatMessage key={message.id} message={message} />
          ))}
          {isLoading && (
            <div className="flex justify-center py-4">
              <div className="animate-pulse text-[#8866a7]">Give me a moment… I'm analyzing your input to generate the most relevant response.</div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      </div>

      <div className="sticky bottom-0 z-10 border-t border-gray-200 bg-white">
        <div className="px-6 py-4">
          <ChatInput
            onSendMessage={handleSendMessage}
            selectedSubTheme={subTheme.id}
            disabled={isLoading}
          />
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
