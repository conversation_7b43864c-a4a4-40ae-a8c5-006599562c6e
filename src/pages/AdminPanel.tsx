import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import { UserProfile, Assignment, AssignmentQuestion, AssignmentWithDetails } from '../types';
import {
  Users,
  Shield,
  Crown,
  User,
  Mail,
  Briefcase,
  Building2,
  Calendar,
  Search,
  RefreshCw,
  FileText,
  Plus,
  Trash2,
  Send,
  ShieldX,
  UserCheck,
  UserX,
  Clock,
  CheckCircle
} from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { secureLog } from '../utils/logger';

interface AdminPanelProps {}

const AdminPanel: React.FC<AdminPanelProps> = () => {
  const { isAdmin, profile } = useAuth();
  const [activeTab, setActiveTab] = useState<'users' | 'assignments'>('users');

  // État pour les utilisateurs
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<'all' | 'user' | 'admin' | 'super_admin'>('all');
  const [sortBy, setSortBy] = useState<'created_at' | 'name' | 'email' | 'role'>('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // État pour les assignements
  const [assignments, setAssignments] = useState<AssignmentWithDetails[]>([]);
  const [assignmentsLoading, setAssignmentsLoading] = useState(false);
  const [showCreateAssignment, setShowCreateAssignment] = useState(false);
  const [showAssignToUsers, setShowAssignToUsers] = useState<string | null>(null);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [currentAssignmentUsers, setCurrentAssignmentUsers] = useState<string[]>([]);
  const [newAssignment, setNewAssignment] = useState<Partial<Assignment>>({
    title: '',
    description: '',
    context: '',
    instructions: '',
    questions: []
  });
  const [newQuestion, setNewQuestion] = useState('');

  // Vérifier les permissions d'accès
  if (!isAdmin()) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-sm p-8 max-w-md w-full">
          <div className="text-center">
            <ShieldX className="mx-auto h-16 w-16 text-red-500 mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Accès refusé</h2>
            <p className="text-gray-600 mb-6">
              Vous devez être administrateur pour accéder à cette section.
            </p>
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-700">
                Cette page est réservée aux administrateurs et super administrateurs de la plateforme.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  useEffect(() => {
    loadUsers();
    if (activeTab === 'assignments') {
      loadAssignments();
    }
  }, [activeTab]);

  const loadUsers = async () => {
    // Éviter les chargements multiples simultanés
    if (loadingUsers) {
      secureLog.info('⏸️ AdminPanel: Chargement déjà en cours, ignoré');
      return;
    }

    try {
      setLoadingUsers(true);
      setLoading(true);
      secureLog.info('🔄 AdminPanel: Chargement des utilisateurs...');

      // Première tentative : requête normale
      let { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error || !data || data.length < 2) {
        secureLog.info('📡 Utilisation de la fonction RPC pour contourner RLS');

        // Tentative avec la fonction RPC
        try {
          const { data: rpcData, error: rpcError } = await supabase
            .rpc('get_all_profiles_for_admin');

          if (!rpcError && rpcData) {
            secureLog.info('✅ RPC Success: Tous les profils récupérés');
            data = rpcData;
            error = null;
          } else {
            secureLog.warn('❌ RPC failed, utilisation des données partielles');
          }
        } catch (rpcErr) {
          secureLog.warn('❌ Exception RPC', rpcErr);
        }
      }

      secureLog.info('📊 AdminPanel: Nombre d\'utilisateurs récupérés', data?.length || 0);
      setUsers(data || []);

    } catch (error) {
      secureLog.error('💥 AdminPanel: Erreur lors du chargement', error);
    } finally {
      setLoading(false);
      setLoadingUsers(false);
    }
  };

    const loadAssignments = async () => {
    try {
      setAssignmentsLoading(true);
      console.log('🔄 Chargement des assignements...');

      // Récupérer les assignements avec leurs questions (ordre par order_index)
      const { data: assignmentsData, error: assignmentsError } = await supabase
        .from('assignments')
        .select(`
          *,
          assignment_questions (
            id,
            question,
            question_type,
            options,
            is_required,
            order_index
          )
        `)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (assignmentsError) {
        console.error('❌ Erreur lors du chargement des assignements:', assignmentsError);
        alert(`Erreur lors du chargement des assignements: ${assignmentsError.message}`);
        return;
      }

      console.log('✅ Assignements récupérés:', assignmentsData?.length || 0);

      // Récupérer les assignations avec les profils utilisateurs
      const { data: userAssignmentsData, error: userAssignmentsError } = await supabase
        .from('user_assignments')
        .select(`
          *,
          profiles (
            id,
            first_name,
            last_name,
            email,
            profession,
            company_name,
            role
          )
        `);

      if (userAssignmentsError) {
        console.error('❌ Erreur lors du chargement des assignations:', userAssignmentsError);
        // On continue même si cette requête échoue
      }

      console.log('✅ Assignations récupérées:', userAssignmentsData?.length || 0);
      if (userAssignmentsData && userAssignmentsData.length > 0) {
        console.log('📋 Détail des assignations:', userAssignmentsData.map(ua => ({
          assignment_id: ua.assignment_id,
          user_id: ua.user_id,
          user_email: ua.profiles?.email,
          user_name: `${ua.profiles?.first_name} ${ua.profiles?.last_name}`
        })));
      }

      // Transformer les données pour matcher notre type AssignmentWithDetails
      const transformedAssignments: AssignmentWithDetails[] = (assignmentsData || []).map(assignment => {
        // Trier les questions par order_index
        const sortedQuestions = (assignment.assignment_questions || []).sort((a: any, b: any) => a.order_index - b.order_index);

        // Filtrer les assignations pour cet assignement
        const assignedUsers = userAssignmentsData
          ?.filter(ua => ua.assignment_id === assignment.id)
          .map(ua => ua.profiles)
          .filter(profile => profile !== null) as UserProfile[] || [];

        console.log(`📋 Assignement "${assignment.title}" (${assignment.id}):`, {
          totalAssignations: userAssignmentsData?.filter(ua => ua.assignment_id === assignment.id).length || 0,
          usersAssigned: assignedUsers.map(u => u?.email || 'email_missing')
        });

        return {
          ...assignment,
          questions: sortedQuestions,
          assigned_to: assignedUsers
        };
      });

      console.log('✅ Assignements transformés:', transformedAssignments.length);
      setAssignments(transformedAssignments);
    } catch (error) {
      console.error('💥 Erreur générale:', error);
      alert(`Erreur inattendue: ${error}`);
    } finally {
      setAssignmentsLoading(false);
    }
  };

  const createAssignment = async () => {
    console.log('🚀 Tentative de création d\'assignement...');
    console.log('📋 Données du formulaire:', {
      title: newAssignment.title,
      description: newAssignment.description,
      context: newAssignment.context,
      instructions: newAssignment.instructions,
      questionsCount: (newAssignment.questions || []).length
    });

    if (!profile || !newAssignment.title || !newAssignment.description || !newAssignment.context || (newAssignment.questions || []).length === 0) {
      alert('Veuillez remplir tous les champs requis et ajouter au moins une question.');
      return;
    }

    try {
      // Créer l'assignement
      console.log('📝 Création de l\'assignement principal...');
      const { data: assignmentData, error: assignmentError } = await supabase
        .from('assignments')
        .insert({
          title: newAssignment.title,
          description: newAssignment.description,
          context: newAssignment.context,
          instructions: newAssignment.instructions || null,
          created_by: profile.id,
          is_active: true
        })
        .select()
        .single();

      if (assignmentError) {
        console.error('❌ Erreur lors de la création de l\'assignement:', assignmentError);
        alert(`Erreur lors de la création de l'assignement: ${assignmentError.message}`);
        return;
      }

      console.log('✅ Assignement créé:', assignmentData);

      // Créer les questions
      console.log('❓ Création des questions...');
      const questionsToInsert = (newAssignment.questions || []).map((q, index) => ({
        assignment_id: assignmentData.id,
        question: q.question,
        question_type: 'textarea',
        is_required: true,
        order_index: index
      }));

      console.log('📝 Questions à insérer:', questionsToInsert);

      const { error: questionsError } = await supabase
        .from('assignment_questions')
        .insert(questionsToInsert);

      if (questionsError) {
        console.error('❌ Erreur lors de la création des questions:', questionsError);
        alert(`Erreur lors de la création des questions: ${questionsError.message}`);
        return;
      }

      console.log('✅ Questions créées avec succès');

      // Réinitialiser le formulaire
      setNewAssignment({
        title: '',
        description: '',
        context: '',
        instructions: '',
        questions: []
      });
      setNewQuestion('');
      setShowCreateAssignment(false);
      alert('Assignement créé avec succès !');

      // Recharger la liste
      console.log('🔄 Rechargement de la liste...');
      loadAssignments();
    } catch (error) {
      console.error('💥 Erreur générale:', error);
      alert(`Erreur lors de la création de l'assignement: ${error}`);
    }
  };

  const addQuestion = () => {
    if (!newQuestion.trim()) return;

    setNewAssignment(prev => ({
      ...prev,
      questions: [
        ...(prev.questions || []),
        {
          id: `temp-${Date.now()}`,
          question: newQuestion.trim(),
          order_index: (prev.questions || []).length,
          assignment_id: 'temp'
        }
      ]
    }));
    setNewQuestion('');
  };

  const removeQuestion = (index: number) => {
    setNewAssignment(prev => ({
      ...prev,
      questions: (prev.questions || []).filter((_, i) => i !== index)
    }));
  };

    const assignToUsers = async (assignmentId: string, userIds: string[]) => {
    console.log('👥 Assignation d\'utilisateurs...', {
      assignmentId,
      newUserIds: userIds,
      currentUserIds: currentAssignmentUsers
    });

    try {
      // 1. Supprimer les assignations existantes qui ne sont plus sélectionnées
      const usersToRemove = currentAssignmentUsers.filter(id => !userIds.includes(id));
      if (usersToRemove.length > 0) {
        console.log('🗑️ Suppression des assignations:', usersToRemove);
        const { error: deleteError } = await supabase
          .from('user_assignments')
          .delete()
          .eq('assignment_id', assignmentId)
          .in('user_id', usersToRemove);

        if (deleteError) {
          console.error('❌ Erreur lors de la suppression:', deleteError);
          alert(`Erreur lors de la suppression des assignations: ${deleteError.message}`);
          return;
        }
      }

      // 2. Ajouter les nouvelles assignations
      const usersToAdd = userIds.filter(id => !currentAssignmentUsers.includes(id));
      if (usersToAdd.length > 0) {
        console.log('➕ Ajout des nouvelles assignations:', usersToAdd);
        const assignmentsToInsert = usersToAdd.map(userId => ({
          assignment_id: assignmentId,
          user_id: userId,
          assigned_by: profile?.id,
          status: 'assigned'
        }));

        const { error: insertError } = await supabase
          .from('user_assignments')
          .insert(assignmentsToInsert);

        if (insertError) {
          console.error('❌ Erreur lors de l\'ajout:', insertError);
          alert(`Erreur lors de l'ajout des assignations: ${insertError.message}`);
          return;
        }
      }

      console.log('✅ Assignation mise à jour avec succès');
      setShowAssignToUsers(null);
      setSelectedUsers([]);
      setCurrentAssignmentUsers([]);
      setSearchTerm('');

      const addedCount = usersToAdd.length;
      const removedCount = usersToRemove.length;
      let message = 'Assignations mises à jour !';
      if (addedCount > 0 && removedCount > 0) {
        message = `${addedCount} utilisateur(s) ajouté(s), ${removedCount} retiré(s)`;
      } else if (addedCount > 0) {
        message = `${addedCount} utilisateur(s) ajouté(s)`;
      } else if (removedCount > 0) {
        message = `${removedCount} utilisateur(s) retiré(s)`;
      }

      alert(message);
      loadAssignments();
    } catch (error) {
      console.error('💥 Erreur générale:', error);
      alert(`Erreur lors de l'assignation: ${error}`);
    }
  };

  const deleteAssignment = async (assignmentId: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cet assignement ? Cette action supprimera également toutes les assignations et réponses associées.')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('assignments')
        .delete()
        .eq('id', assignmentId);

      if (error) {
        console.error('Erreur lors de la suppression:', error);
        alert('Erreur lors de la suppression');
        return;
      }

      alert('Assignement supprimé avec succès !');
      loadAssignments();
    } catch (error) {
      console.error('Erreur:', error);
      alert('Erreur lors de la suppression');
    }
  };

  const handleUserSelection = (userId: string, checked: boolean) => {
    if (checked) {
      setSelectedUsers(prev => [...prev, userId]);
    } else {
      setSelectedUsers(prev => prev.filter(id => id !== userId));
    }
  };

  // Fonction pour filtrer les utilisateurs assignables (tous les rôles)
  const getAssignableUsers = () => {
    return users.filter(user =>
      user.role === 'user' || user.role === 'admin' || user.role === 'super_admin'
    );
  };

  // Fonction pour filtrer selon la recherche
  const filterUsersBySearch = (usersList: UserProfile[]) => {
    return usersList.filter(user =>
      searchTerm === '' ||
      `${user.first_name} ${user.last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.company_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.profession.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.role.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'super_admin':
        return <Crown className="w-4 h-4 text-purple-600" />;
      case 'admin':
        return <Shield className="w-4 h-4 text-blue-600" />;
      default:
        return <User className="w-4 h-4 text-gray-600" />;
    }
  };

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'super_admin':
        return (
          <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 border border-purple-200">
            <Crown className="w-3 h-3" />
            Super Admin
          </span>
        );
      case 'admin':
        return (
          <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
            <Shield className="w-3 h-3" />
            Admin
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200">
            <User className="w-3 h-3" />
            Utilisateur
          </span>
        );
    }
  };

  const getCompletionStatus = (user: UserProfile) => {
    const isComplete = user.first_name !== 'To complete' &&
                      user.last_name !== 'To complete' &&
                      user.profession !== 'To complete' &&
                      user.company_name !== 'To complete';

    return isComplete ? (
      <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
        <UserCheck className="w-3 h-3" />
        Complet
      </span>
    ) : (
      <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 border border-orange-200">
        <UserX className="w-3 h-3" />
        Incomplet
      </span>
    );
  };

  // Filtrage et tri des utilisateurs
  const filteredAndSortedUsers = users
    .filter(user => {
      const matchesSearch = searchTerm === '' ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.company_name.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesRole = roleFilter === 'all' || user.role === roleFilter;

      return matchesSearch && matchesRole;
    })
    .sort((a, b) => {
      let valueA: any, valueB: any;

      switch (sortBy) {
        case 'name':
          valueA = `${a.first_name} ${a.last_name}`.toLowerCase();
          valueB = `${b.first_name} ${b.last_name}`.toLowerCase();
          break;
        case 'email':
          valueA = a.email.toLowerCase();
          valueB = b.email.toLowerCase();
          break;
        case 'role':
          valueA = a.role;
          valueB = b.role;
          break;
        case 'created_at':
        default:
          valueA = new Date(a.created_at);
          valueB = new Date(b.created_at);
          break;
      }

      if (sortOrder === 'asc') {
        return valueA > valueB ? 1 : -1;
      } else {
        return valueA < valueB ? 1 : -1;
      }
    });

  // Debug final (seulement si nécessaire)
  if (process.env.NODE_ENV === 'development' && users.length !== filteredAndSortedUsers.length) {
    secureLog.info('📋 AdminPanel: Filtrage appliqué', {
      totalUsers: users.length,
      filteredUsers: filteredAndSortedUsers.length,
      searchTerm,
      roleFilter
    });
  }

  const handleSort = (field: typeof sortBy) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const getRoleStats = () => {
    const stats = users.reduce((acc, user) => {
      acc[user.role] = (acc[user.role] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return [
      { role: 'super_admin', count: stats.super_admin || 0, label: 'Super Admins', color: 'purple' },
      { role: 'admin', count: stats.admin || 0, label: 'Admins', color: 'blue' },
      { role: 'user', count: stats.user || 0, label: 'Utilisateurs', color: 'gray' },
    ];
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="mx-auto h-8 w-8 text-blue-600 animate-spin mb-4" />
          <p className="text-gray-600">Chargement...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* En-tête */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-blue-100 rounded-lg">
              {activeTab === 'users' ? <Users className="w-6 h-6 text-blue-600" /> : <FileText className="w-6 h-6 text-blue-600" />}
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Panel Administration</h1>
              <p className="text-gray-600">
                {activeTab === 'users' ? 'Gestion des utilisateurs de la plateforme' : 'Gestion des assignements'}
              </p>
            </div>
          </div>

          {/* Onglets */}
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6">
            <button
              onClick={() => setActiveTab('users')}
              className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'users'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <Users className="w-4 h-4" />
              Utilisateurs
            </button>
            <button
              onClick={() => setActiveTab('assignments')}
              className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'assignments'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              <FileText className="w-4 h-4" />
              Assignements
            </button>
          </div>
        </div>

        {/* Contenu pour l'onglet Utilisateurs */}
        {activeTab === 'users' && (
          <div className="space-y-6">
            {/* Statistiques */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg p-6 shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total utilisateurs</p>
                    <p className="text-2xl font-bold text-gray-900">{users.length}</p>
                  </div>
                  <Users className="w-8 h-8 text-gray-400" />
                </div>
              </div>

              {getRoleStats().map((stat) => (
                <div key={stat.role} className="bg-white rounded-lg p-6 shadow-sm border">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                      <p className="text-2xl font-bold text-gray-900">{stat.count}</p>
                    </div>
                    {getRoleIcon(stat.role)}
                  </div>
                </div>
              ))}
            </div>

            {/* Filtres et recherche */}
            <div className="bg-white rounded-lg p-6 shadow-sm border">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="Rechercher par nom, email ou entreprise..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="flex gap-2">
                  <select
                    value={roleFilter}
                    onChange={(e) => setRoleFilter(e.target.value as any)}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="all">Tous les rôles</option>
                    <option value="super_admin">Super Admins</option>
                    <option value="admin">Admins</option>
                    <option value="user">Utilisateurs</option>
                  </select>

                  <button
                    onClick={loadUsers}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                  >
                    <RefreshCw className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>

            {/* Table des utilisateurs */}
            <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th
                        onClick={() => handleSort('name')}
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      >
                        <div className="flex items-center gap-1">
                          Utilisateur
                          {sortBy === 'name' && (
                            <span className="text-blue-600">
                              {sortOrder === 'asc' ? '↑' : '↓'}
                            </span>
                          )}
                        </div>
                      </th>
                      <th
                        onClick={() => handleSort('email')}
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      >
                        <div className="flex items-center gap-1">
                          Email
                          {sortBy === 'email' && (
                            <span className="text-blue-600">
                              {sortOrder === 'asc' ? '↑' : '↓'}
                            </span>
                          )}
                        </div>
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Entreprise
                      </th>
                      <th
                        onClick={() => handleSort('role')}
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      >
                        <div className="flex items-center gap-1">
                          Rôle
                          {sortBy === 'role' && (
                            <span className="text-blue-600">
                              {sortOrder === 'asc' ? '↑' : '↓'}
                            </span>
                          )}
                        </div>
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Statut
                      </th>
                      <th
                        onClick={() => handleSort('created_at')}
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                      >
                        <div className="flex items-center gap-1">
                          Inscription
                          {sortBy === 'created_at' && (
                            <span className="text-blue-600">
                              {sortOrder === 'asc' ? '↑' : '↓'}
                            </span>
                          )}
                        </div>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredAndSortedUsers.map((user) => (
                      <tr key={user.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-medium">
                                {user.first_name.charAt(0)}{user.last_name.charAt(0)}
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {user.first_name} {user.last_name}
                              </div>
                              <div className="text-sm text-gray-500 flex items-center gap-1">
                                <Briefcase className="w-3 h-3" />
                                {user.profession}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center gap-1 text-sm text-gray-900">
                            <Mail className="w-3 h-3 text-gray-400" />
                            {user.email}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center gap-1 text-sm text-gray-900">
                            <Building2 className="w-3 h-3 text-gray-400" />
                            {user.company_name}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getRoleBadge(user.role)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getCompletionStatus(user)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            {format(new Date(user.created_at), 'dd/MM/yyyy', { locale: fr })}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {filteredAndSortedUsers.length === 0 && (
                <div className="text-center py-12">
                  <Users className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Aucun utilisateur trouvé
                  </h3>
                  <p className="text-gray-500">
                    {searchTerm || roleFilter !== 'all'
                      ? 'Essayez de modifier vos critères de recherche.'
                      : 'Aucun utilisateur enregistré sur la plateforme.'}
                  </p>
                </div>
              )}
            </div>

            {/* Résumé en bas */}
            <div className="bg-white rounded-lg p-4 shadow-sm border">
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>
                  Affichage de {filteredAndSortedUsers.length} sur {users.length} utilisateurs
                </span>
                <span>
                  Dernière mise à jour : {format(new Date(), 'dd/MM/yyyy à HH:mm', { locale: fr })}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Contenu pour l'onglet Assignements */}
        {activeTab === 'assignments' && (
          <div className="space-y-6">
            {/* En-tête des assignements avec bouton créer */}
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Gestion des Assignements</h2>
                <p className="text-gray-600">Créez et assignez des exercices aux utilisateurs</p>
              </div>
              <button
                onClick={() => setShowCreateAssignment(true)}
                className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
              >
                <Plus className="w-4 h-4" />
                Créer un assignement
              </button>
            </div>

            {/* Liste des assignements */}
            {assignmentsLoading ? (
              <div className="flex items-center justify-center py-12">
                <RefreshCw className="w-6 h-6 text-blue-600 animate-spin mr-2" />
                <span className="text-gray-600">Chargement des assignements...</span>
              </div>
            ) : assignments.length === 0 ? (
              <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
                <FileText className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Aucun assignement</h3>
                <p className="text-gray-500 mb-4">Créez votre premier assignement pour commencer.</p>
                <button
                  onClick={() => setShowCreateAssignment(true)}
                  className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  <Plus className="w-4 h-4" />
                  Créer un assignement
                </button>
              </div>
            ) : (
              <div className="grid gap-4">
                {assignments.map((assignment) => (
                  <div key={assignment.id} className="bg-white rounded-lg border border-gray-200 p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">{assignment.title}</h3>
                        <p className="text-gray-600 mb-2">{assignment.description}</p>
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span className="flex items-center gap-1">
                            <FileText className="w-4 h-4" />
                            {assignment.questions?.length || 0} questions
                          </span>
                          <span className="flex items-center gap-1">
                            <Users className="w-4 h-4" />
                            {assignment.assigned_to?.length || 0} assigné(s)
                          </span>
                          <span className="flex items-center gap-1">
                            <Calendar className="w-4 h-4" />
                            {format(new Date(assignment.created_at), 'dd/MM/yyyy', { locale: fr })}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => {
                            const assignedUserIds = assignment.assigned_to?.map(user => user.id) || [];
                            console.log('👥 Ouverture modal assignation pour:', assignment.id, 'Utilisateurs déjà assignés:', assignedUserIds);
                            setCurrentAssignmentUsers(assignedUserIds);
                            setSelectedUsers(assignedUserIds);
                            setShowAssignToUsers(assignment.id);
                          }}
                          className="inline-flex items-center gap-1 px-3 py-1.5 text-sm bg-green-50 text-green-700 rounded-md hover:bg-green-100 transition-colors"
                        >
                          <Send className="w-3 h-3" />
                          Assigner
                        </button>
                        <button
                          onClick={() => deleteAssignment(assignment.id)}
                          className="inline-flex items-center gap-1 px-3 py-1.5 text-sm bg-red-50 text-red-700 rounded-md hover:bg-red-100 transition-colors"
                        >
                          <Trash2 className="w-3 h-3" />
                          Supprimer
                        </button>
                      </div>
                    </div>

                    {/* Aperçu des questions */}
                    <div className="bg-gray-50 rounded-md p-3">
                      <p className="text-sm font-medium text-gray-700 mb-2">Questions :</p>
                      <div className="space-y-1">
                        {assignment.questions?.slice(0, 2).map((q, index) => (
                          <p key={q.id} className="text-sm text-gray-600">
                            {index + 1}. {q.question}
                          </p>
                        ))}
                        {assignment.questions && assignment.questions.length > 2 && (
                          <p className="text-sm text-gray-500 italic">
                            ... et {assignment.questions.length - 2} autres questions
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Liste des utilisateurs assignés */}
                    {assignment.assigned_to && assignment.assigned_to.length > 0 && (
                      <div className="mt-4 pt-4 border-t border-gray-200">
                        <p className="text-sm font-medium text-gray-700 mb-2">Assigné à :</p>
                        <div className="flex flex-wrap gap-2">
                          {assignment.assigned_to.map((user: UserProfile) => (
                            <span key={user.id} className={`inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs ${
                              user.role === 'super_admin' ? 'bg-purple-50 text-purple-700' :
                              user.role === 'admin' ? 'bg-blue-50 text-blue-700' :
                              'bg-green-50 text-green-700'
                            }`}>
                              {getRoleIcon(user.role)}
                              {user.first_name} {user.last_name}
                              {user.role !== 'user' && (
                                <span className="text-xs opacity-75">({user.role})</span>
                              )}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Modal de création d'assignement */}
        {showCreateAssignment && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-gray-900">Créer un nouvel assignement</h2>
                  <button
                    onClick={() => setShowCreateAssignment(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ✕
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Titre *
                    </label>
                    <input
                      type="text"
                      value={newAssignment.title || ''}
                      onChange={(e) => setNewAssignment(prev => ({ ...prev, title: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Titre de l'assignement"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Description *
                    </label>
                    <textarea
                      value={newAssignment.description || ''}
                      onChange={(e) => setNewAssignment(prev => ({ ...prev, description: e.target.value }))}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Description de l'assignement"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Contexte *
                    </label>
                    <textarea
                      value={newAssignment.context || ''}
                      onChange={(e) => setNewAssignment(prev => ({ ...prev, context: e.target.value }))}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Contexte détaillé de l'assignement"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Instructions pour les utilisateurs
                    </label>
                    <textarea
                      value={newAssignment.instructions || ''}
                      onChange={(e) => setNewAssignment(prev => ({ ...prev, instructions: e.target.value }))}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Instructions spécifiques pour guider les utilisateurs dans leurs réponses"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Questions *
                    </label>

                    {/* Liste des questions */}
                    <div className="space-y-2 mb-3">
                      {(newAssignment.questions || []).map((question, index) => (
                        <div key={index} className="flex items-start gap-2 p-3 bg-gray-50 rounded-md">
                          <span className="text-sm font-medium text-gray-500 mt-1">{index + 1}.</span>
                          <span className="flex-1 text-sm text-gray-700">{question.question}</span>
                          <button
                            onClick={() => removeQuestion(index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>

                    {/* Ajouter une question */}
                    <div className="flex gap-2">
                      <input
                        type="text"
                        value={newQuestion}
                        onChange={(e) => setNewQuestion(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && addQuestion()}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Nouvelle question..."
                      />
                      <button
                        onClick={addQuestion}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end gap-3 mt-6 pt-6 border-t">
                  <button
                    onClick={() => setShowCreateAssignment(false)}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                  >
                    Annuler
                  </button>
                  <button
                    onClick={createAssignment}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    Créer l'assignement
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Modal d'assignation à des utilisateurs - Version améliorée */}
        {showAssignToUsers && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] flex flex-col">
              <div className="p-6 border-b">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-gray-900">Assigner aux utilisateurs</h2>
                  <button
                    onClick={() => {
                      setShowAssignToUsers(null);
                      setSelectedUsers([]);
                      setCurrentAssignmentUsers([]);
                      setSearchTerm('');
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ✕
                  </button>
                </div>

                {/* Barre de recherche */}
                <div className="space-y-3">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="Rechercher par nom, email, entreprise, profession ou rôle..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div className="text-sm text-gray-600 bg-blue-50 p-2 rounded-md">
                    💡 <strong>Nouveau :</strong> Vous pouvez maintenant assigner des exercices aux utilisateurs, admins et super-admins.
                  </div>
                </div>
              </div>

              <div className="flex-1 overflow-hidden">
                {/* Résumé sélection */}
                {selectedUsers.length > 0 && (
                  <div className="px-6 py-3 bg-blue-50 border-b">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-blue-700">
                        {selectedUsers.length} utilisateur(s) sélectionné(s)
                      </span>
                      <button
                        onClick={() => setSelectedUsers([])}
                        className="text-sm text-blue-600 hover:text-blue-800"
                      >
                        Tout déselectionner
                      </button>
                    </div>
                  </div>
                )}

                {/* Liste des utilisateurs */}
                <div className="p-6 overflow-y-auto">
                  <div className="space-y-2">
                                                            {filterUsersBySearch(getAssignableUsers())
                      .map((user) => (
                        <label key={user.id} className="flex items-center gap-4 p-3 hover:bg-gray-50 rounded-lg cursor-pointer border border-transparent hover:border-gray-200 transition-all">
                          <input
                            type="checkbox"
                            checked={selectedUsers.includes(user.id)}
                            onChange={(e) => handleUserSelection(user.id, e.target.checked)}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 w-4 h-4"
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <div className="text-sm font-medium text-gray-900">
                                {user.first_name} {user.last_name}
                              </div>
                              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                {user.profession}
                              </span>
                              {getRoleBadge(user.role)}
                            </div>
                            <div className="text-xs text-gray-500 mb-1">
                              <Mail className="w-3 h-3 inline mr-1" />
                              {user.email}
                            </div>
                            <div className="text-xs text-gray-500">
                              <Building2 className="w-3 h-3 inline mr-1" />
                              {user.company_name}
                            </div>
                          </div>
                        </label>
                      ))}
                  </div>

                                                      {filterUsersBySearch(getAssignableUsers()).length === 0 && (
                    <div className="text-center py-8">
                      <User className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                      <p className="text-gray-500">
                        {searchTerm ? 'Aucun utilisateur trouvé pour cette recherche.' : 'Aucun utilisateur disponible pour assignation.'}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              <div className="p-6 border-t bg-gray-50">
                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-600">
                    {getAssignableUsers().length} utilisateur(s) au total
                  </div>
                  <div className="flex gap-3">
                    <button
                      onClick={() => {
                        setShowAssignToUsers(null);
                        setSelectedUsers([]);
                        setCurrentAssignmentUsers([]);
                        setSearchTerm('');
                      }}
                      className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                    >
                      Annuler
                    </button>
                    <button
                      onClick={() => {
                        if (showAssignToUsers && selectedUsers.length > 0) {
                          assignToUsers(showAssignToUsers, selectedUsers);
                        }
                      }}
                      disabled={selectedUsers.length === 0}
                      className={`px-6 py-2 rounded-md transition-colors ${
                        selectedUsers.length > 0
                          ? 'bg-blue-600 hover:bg-blue-700 text-white'
                          : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      }`}
                    >
                      <Send className="w-4 h-4 inline mr-2" />
                      Assigner à {selectedUsers.length} utilisateur(s)
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminPanel;
