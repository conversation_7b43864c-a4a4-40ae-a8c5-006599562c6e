import React from 'react';
import { Theme } from '../types';
import ThemeCard from '../components/ThemeCard';
import Footer from '../components/Footer';

interface ThemeSelectionProps {
  themes: Theme[];
  onSelectTheme: (themeId: number) => void;
}

const ThemeSelection: React.FC<ThemeSelectionProps> = ({ themes, onSelectTheme }) => {
  return (
    <div className="min-h-full flex flex-col">
      <div className="p-6 max-w-7xl mx-auto w-full">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-1">I would like to...</h1>
          <p className="text-gray-600">Select a theme to explore leadership insights and guidance</p>
        </div>

        <div className="grid md:grid-cols-2 gap-4">
          {themes.map(theme => (
            <ThemeCard
              key={theme.id}
              theme={theme}
              onClick={onSelectTheme}
            />
          ))}
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default ThemeSelection;
