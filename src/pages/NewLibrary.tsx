import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/AuthContext';
import {
  FolderIcon, FileIcon, ChevronDownIcon, ChevronRightIcon,
  LockIcon, XIcon, FileTextIcon, SearchIcon, PlayIcon,
  Eye, Download, ExternalLink, Tag, Calendar, User
} from 'lucide-react';
import Footer from '../components/Footer';
import Breadcrumbs, { BreadcrumbItem } from '../components/Breadcrumbs';

interface Document {
  id: string;
  title: string;
  description: string;
  file_path: string;
  file_type: 'pdf' | 'video' | 'youtube' | 'image';
  file_size_bytes: number;
  theme_id: number;
  sub_theme_id: number;
  youtube_video_id?: string;
  tags: string[];
  is_public: boolean;
  created_at: string;
  access_granted_at?: string;
  access_expires_at?: string;
}

interface Category {
  id: string;
  name: string;
  description: string;
  icon_name?: string;
  color?: string;
  document_count: number;
}

const NewLibrary: React.FC = () => {
  const { profile } = useAuth();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [expandedCategories, setExpandedCategories] = useState<string[]>(['all']);

  useEffect(() => {
    loadUserDocuments();
    loadCategories();
  }, []);

  const loadUserDocuments = async () => {
    try {
      // Charger les documents accessibles à l'utilisateur actuel
      const { data, error } = await supabase.rpc('get_user_accessible_documents');

      if (error) throw error;
      setDocuments(data || []);
    } catch (error) {
      console.error('Erreur lors du chargement des documents:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('document_categories')
        .select(`
          *,
          document_category_links(count)
        `)
        .eq('is_active', true)
        .order('display_order');

      if (error) throw error;

      const categoriesWithCount = data?.map(cat => ({
        ...cat,
        document_count: cat.document_category_links?.length || 0
      })) || [];

      setCategories(categoriesWithCount);
    } catch (error) {
      console.error('Erreur lors du chargement des catégories:', error);
    }
  };

  const getDocumentUrl = (document: Document) => {
    if (document.file_type === 'youtube') {
      return `https://www.youtube.com/embed/${document.youtube_video_id}`;
    } else {
      // URL pour Supabase Storage
      const { data } = supabase.storage.from('documents').getPublicUrl(document.file_path);
      return data.publicUrl;
    }
  };

  const handleDocumentClick = (doc: Document) => {
    setSelectedDocument(doc);
  };

  const closePreview = () => {
    setSelectedDocument(null);
  };

  const clearSearch = () => {
    setSearchQuery('');
  };

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case 'pdf': return <FileTextIcon size={16} className="text-red-500" />;
      case 'video': return <PlayIcon size={16} className="text-blue-500" />;
      case 'youtube': return <PlayIcon size={16} className="text-red-600" />;
      default: return <FileIcon size={16} className="text-gray-500" />;
    }
  };

  const getCategoryIcon = (iconName?: string) => {
    switch (iconName) {
      case 'Crown': return '👑';
      case 'Brain': return '🧠';
      case 'Users': return '👥';
      case 'Play': return '🎬';
      default: return '📁';
    }
  };

  const getFilteredDocuments = () => {
    let filtered = documents;

    // Filtrer par recherche
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(doc =>
        doc.title.toLowerCase().includes(query) ||
        doc.description?.toLowerCase().includes(query) ||
        doc.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Filtrer par catégorie (à implémenter si nécessaire)
    // if (selectedCategory !== 'all') {
    //   filtered = filtered.filter(doc => doc.category_id === selectedCategory);
    // }

    return filtered;
  };

  const filteredDocuments = getFilteredDocuments();

  const breadcrumbItems: BreadcrumbItem[] = [
    {
      label: 'Library',
      isActive: true
    }
  ];

  if (loading) {
    return (
      <div className="min-h-full flex flex-col">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-full flex flex-col">
      <div className="p-6 max-w-7xl mx-auto w-full relative">
        <Breadcrumbs
          items={breadcrumbItems}
          className="mb-6"
        />

        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 mb-1">Bibliothèque de Documents</h1>
            <p className="text-gray-600">
              Accédez aux documents qui vous ont été assignés ({filteredDocuments.length} disponibles)
            </p>
          </div>

          <div className="w-64 relative">
            <div className="relative">
              <SearchIcon size={16} className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Rechercher des documents..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-8 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
              {searchQuery && (
                <button
                  onClick={clearSearch}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <XIcon size={16} />
                </button>
              )}
            </div>
          </div>
        </div>

        <div className="flex gap-6 h-[calc(100vh-12rem)] relative">
          {/* Sidebar avec catégories */}
          <div className="w-1/3 bg-white rounded-lg border border-gray-200 overflow-y-auto relative z-0">
            <div className="p-4">
              <h3 className="font-semibold text-gray-800 mb-4">Catégories</h3>

              {/* Tous les documents */}
              <div className="space-y-1">
                <button
                  onClick={() => setSelectedCategory('all')}
                  className={`
                    w-full flex items-center justify-between px-3 py-2 rounded-lg transition-colors text-left
                    ${selectedCategory === 'all' ? 'bg-purple-50 text-purple-700' : 'hover:bg-gray-50'}
                  `}
                >
                  <div className="flex items-center gap-2">
                    <span>📚</span>
                    <span className="text-sm font-medium">Tous les documents</span>
                  </div>
                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                    {documents.length}
                  </span>
                </button>

                {/* Documents publics */}
                <button
                  onClick={() => setSelectedCategory('public')}
                  className={`
                    w-full flex items-center justify-between px-3 py-2 rounded-lg transition-colors text-left
                    ${selectedCategory === 'public' ? 'bg-purple-50 text-purple-700' : 'hover:bg-gray-50'}
                  `}
                >
                  <div className="flex items-center gap-2">
                    <Eye size={16} className="text-green-500" />
                    <span className="text-sm font-medium">Documents publics</span>
                  </div>
                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                    {documents.filter(d => d.is_public).length}
                  </span>
                </button>

                {/* Documents privés assignés */}
                <button
                  onClick={() => setSelectedCategory('private')}
                  className={`
                    w-full flex items-center justify-between px-3 py-2 rounded-lg transition-colors text-left
                    ${selectedCategory === 'private' ? 'bg-purple-50 text-purple-700' : 'hover:bg-gray-50'}
                  `}
                >
                  <div className="flex items-center gap-2">
                    <User size={16} className="text-blue-500" />
                    <span className="text-sm font-medium">Assignés personnellement</span>
                  </div>
                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                    {documents.filter(d => !d.is_public && d.access_granted_at).length}
                  </span>
                </button>
              </div>

              {/* Catégories personnalisées */}
              {categories.length > 0 && (
                <div className="mt-6">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Catégories</h4>
                  <div className="space-y-1">
                    {categories.map(category => (
                      <button
                        key={category.id}
                        onClick={() => setSelectedCategory(category.id)}
                        className={`
                          w-full flex items-center justify-between px-3 py-2 rounded-lg transition-colors text-left
                          ${selectedCategory === category.id ? 'bg-purple-50 text-purple-700' : 'hover:bg-gray-50'}
                        `}
                      >
                        <div className="flex items-center gap-2">
                          <span>{getCategoryIcon(category.icon_name)}</span>
                          <span className="text-sm">{category.name}</span>
                        </div>
                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                          {category.document_count}
                        </span>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Zone principale avec liste et prévisualisation */}
          <div className="flex-1 flex flex-col gap-4">
            {/* Liste des documents */}
            <div className="flex-1 bg-white rounded-lg border border-gray-200 overflow-hidden">
              {filteredDocuments.length > 0 ? (
                <div className="divide-y divide-gray-200">
                  {filteredDocuments.map((document) => (
                    <div
                      key={document.id}
                      onClick={() => handleDocumentClick(document)}
                      className={`
                        p-4 hover:bg-gray-50 cursor-pointer transition-colors
                        ${selectedDocument?.id === document.id ? 'bg-purple-50 border-r-4 border-purple-500' : ''}
                      `}
                    >
                      <div className="flex items-start gap-3">
                        {getFileIcon(document.file_type)}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h3 className="font-medium text-gray-900 truncate">{document.title}</h3>
                              {document.description && (
                                <p className="text-sm text-gray-600 mt-1 line-clamp-2">{document.description}</p>
                              )}
                            </div>
                            <div className="ml-4 flex items-center gap-2">
                              {document.is_public ? (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-700">
                                  <Eye size={10} className="mr-1" />
                                  Public
                                </span>
                              ) : (
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-700">
                                  <User size={10} className="mr-1" />
                                  Assigné
                                </span>
                              )}
                            </div>
                          </div>

                          <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                            <span className="capitalize">{document.file_type}</span>
                            {document.file_size_bytes && (
                              <span>{formatFileSize(document.file_size_bytes)}</span>
                            )}
                            <span>{new Date(document.created_at).toLocaleDateString('fr-FR')}</span>
                            {document.access_granted_at && !document.is_public && (
                              <span className="flex items-center gap-1 text-blue-600">
                                <Calendar size={10} />
                                Assigné le {new Date(document.access_granted_at).toLocaleDateString('fr-FR')}
                              </span>
                            )}
                            {document.access_expires_at && (
                              <span className="flex items-center gap-1 text-orange-600">
                                <Calendar size={10} />
                                Expire le {new Date(document.access_expires_at).toLocaleDateString('fr-FR')}
                              </span>
                            )}
                          </div>

                          {document.tags && document.tags.length > 0 && (
                            <div className="flex gap-1 mt-2">
                              {document.tags.slice(0, 3).map((tag, index) => (
                                <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-700">
                                  <Tag size={8} className="mr-1" />
                                  {tag}
                                </span>
                              ))}
                              {document.tags.length > 3 && (
                                <span className="text-xs text-gray-500">+{document.tags.length - 3}</span>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                  <FileIcon size={48} className="mb-4 text-gray-300" />
                  <h3 className="text-lg font-medium mb-2">Aucun document disponible</h3>
                  <p className="text-center">
                    {searchQuery ?
                      'Aucun document ne correspond à votre recherche.' :
                      'Aucun document ne vous a encore été assigné.'
                    }
                  </p>
                </div>
              )}
            </div>

            {/* Zone de prévisualisation */}
            {selectedDocument && (
              <div className="h-96 bg-white rounded-lg border border-gray-200 overflow-hidden flex flex-col">
                <div className="flex items-center justify-between p-4 border-b border-gray-200">
                  <div className="flex-1 min-w-0">
                    <h2 className="text-lg font-semibold text-gray-800 truncate">
                      {selectedDocument.title}
                    </h2>
                    <p className="text-sm text-gray-600">{selectedDocument.description}</p>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    {selectedDocument.file_type !== 'youtube' && (
                      <a
                        href={getDocumentUrl(selectedDocument)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
                        title="Ouvrir dans un nouvel onglet"
                      >
                        <ExternalLink size={16} />
                      </a>
                    )}
                    <button
                      onClick={closePreview}
                      className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <XIcon size={16} />
                    </button>
                  </div>
                </div>
                <div className="flex-1 bg-gray-50">
                  {selectedDocument.file_type === 'pdf' ? (
                    <iframe
                      src={getDocumentUrl(selectedDocument)}
                      className="w-full h-full"
                      title={selectedDocument.title}
                    />
                  ) : selectedDocument.file_type === 'youtube' ? (
                    <iframe
                      src={getDocumentUrl(selectedDocument)}
                      className="w-full h-full"
                      title={selectedDocument.title}
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                    />
                  ) : (
                    <video
                      src={getDocumentUrl(selectedDocument)}
                      controls
                      className="w-full h-full bg-black"
                    >
                      Votre navigateur ne supporte pas la lecture vidéo.
                    </video>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default NewLibrary;
