import React from 'react';
import { SubTheme, Theme } from '../types';
import SubThemeCard from '../components/SubThemeCard';
import Footer from '../components/Footer';
import Breadcrumbs, { BreadcrumbItem } from '../components/Breadcrumbs';

interface SubThemeSelectionProps {
  theme: Theme;
  subThemes: SubTheme[];
  onSelectSubTheme: (subThemeId: number) => void;
  onBack: () => void;
}

const SubThemeSelection: React.FC<SubThemeSelectionProps> = ({
  theme,
  subThemes,
  onSelectSubTheme,
  onBack
}) => {
  const breadcrumbItems: BreadcrumbItem[] = [
    {
      label: theme.title,
      isActive: true
    }
  ];

  return (
    <div className="min-h-full flex flex-col">
      <div className="p-6 max-w-7xl mx-auto w-full">
        <Breadcrumbs
          items={breadcrumbItems}
          className="mb-6"
        />

        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-1">{theme.title}</h1>
          <p className="text-gray-600">{theme.description}</p>
        </div>

        <div className="space-y-1">
          <h2 className="text-lg font-medium text-gray-700 mb-3">Select a sub-theme:</h2>
          {subThemes
            .filter(subTheme => subTheme.themeId === theme.id)
            .map(subTheme => (
              <SubThemeCard
                key={subTheme.id}
                subTheme={subTheme}
                onClick={onSelectSubTheme}
              />
            ))
          }
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default SubThemeSelection;
