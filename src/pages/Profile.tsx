import React from 'react';
import InteractiveUserProfile from '../components/InteractiveUserProfile'
import InteractivePsychometricData from '../components/InteractivePsychometricData'
import { useAuth } from '../contexts/AuthContext'
import Footer from '../components/Footer';
import LoadingSpinner from '../components/LoadingSpinner';
import Breadcrumbs, { BreadcrumbItem } from '../components/Breadcrumbs';
import { useNavigate } from 'react-router-dom';

interface ProfileProps {
  onBack?: () => void;
}

const Profile: React.FC<ProfileProps> = () => {
  const { profile, updateProfile } = useAuth();
  const navigate = useNavigate();

  const breadcrumbItems: BreadcrumbItem[] = [
    {
      label: 'Profile',
      isActive: true
    }
  ];

  if (!profile) {
    return (
      <div className="min-h-full flex flex-col">
        <div className="flex-1 flex items-center justify-center">
          <LoadingSpinner text="Loading profile..." />
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-full flex flex-col">
      <div className="p-6 max-w-7xl mx-auto w-full">
                <Breadcrumbs
          items={breadcrumbItems}
          className="mb-6"
        />

        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-1">Your Profile</h1>
          <p className="text-gray-600">Manage your information and access your psychometric data</p>
        </div>

        <div className="space-y-8">
          <InteractiveUserProfile profile={profile} onUpdateProfile={updateProfile} />
          <InteractivePsychometricData profile={profile} />
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Profile;
