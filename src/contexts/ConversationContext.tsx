import React, { createContext, useContext, ReactNode } from 'react';
import { useConversationHistory } from '../hooks/useConversationHistory';
import { ConversationHistory, ChatMessage } from '../types';

interface ConversationContextType {
  conversations: ConversationHistory[];
  addConversation: (theme: string, title: string, initialMessage: ChatMessage) => ConversationHistory;
  addMessageToConversation: (conversationId: string, message: ChatMessage) => void;
  deleteConversation: (conversationId: string) => void;
  clearHistory: () => void;
}

const ConversationContext = createContext<ConversationContextType | undefined>(undefined);

export const ConversationProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const conversationHistory = useConversationHistory();

  return (
    <ConversationContext.Provider value={conversationHistory}>
      {children}
    </ConversationContext.Provider>
  );
};

export const useConversations = () => {
  const context = useContext(ConversationContext);
  if (context === undefined) {
    throw new Error('useConversations must be used within a ConversationProvider');
  }
  return context;
};
