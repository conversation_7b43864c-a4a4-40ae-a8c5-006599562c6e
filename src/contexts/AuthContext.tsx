import React, { createContext, useContext, useEffect, useState } from 'react'
import { User, Session, AuthError } from '@supabase/supabase-js'
import { supabase } from '../lib/supabase'
import { secureLog } from '../utils/logger'

export interface UserProfile {
  id: string
  first_name: string
  last_name: string
  profession: string
  company_name: string
  email: string
  role: 'user' | 'admin' | 'super_admin'
}

interface AuthContextType {
  user: User | null
  profile: UserProfile | null
  session: Session | null
  loading: boolean
  initialized: boolean
  signUp: (email: string, password: string, profileData?: Partial<Omit<UserProfile, 'id' | 'email' | 'role'>>) => Promise<{ error: AuthError | null }>
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>
  signOut: () => Promise<void>
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>
  updatePassword: (password: string) => Promise<{ error: AuthError | null }>
  updateProfile: (updates: Partial<UserProfile>) => Promise<{ error: any | null }>
  isAdmin: () => boolean
  isSuperAdmin: () => boolean
  hasRole: (role: 'user' | 'admin' | 'super_admin') => boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(false) // Start without loading
  const [profileCache, setProfileCache] = useState<{ [key: string]: UserProfile }>({})
  const [initialized, setInitialized] = useState(false)

  useEffect(() => {
    let isMounted = true

            // Get initial session silently
    const initAuth = async () => {
      try {
        secureLog.info('🚀 Initialisation de l\'authentification...')
        // Get session without loading state
        const { data: { session } } = await supabase.auth.getSession()

        if (!isMounted) return

        secureLog.info('🔐 Session initiale récupérée', {
          hasSession: !!session,
          hasUser: !!session?.user,
          userId: session?.user?.id || 'none'
        })

        setSession(session)
        setUser(session?.user || null)

        if (session?.user) {
          // Fetch profile in background
          fetchProfile(session.user.id)
        }
      } catch (error) {
        // Silent error handling
        secureLog.error('❌ Erreur de vérification de session:', error)
        // Retry once after a short delay if it fails
        setTimeout(() => {
          if (isMounted) {
            secureLog.info('🔄 Nouvelle tentative d\'initialisation...')
            initAuth()
          }
        }, 1000)
        return
      } finally {
        if (isMounted) {
          secureLog.info('✅ Authentification initialisée')
          setInitialized(true)
        }
      }
    }

    initAuth()

    // Listen to auth changes silently
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (!isMounted) return

      // Utiliser secureLog au lieu de console.log direct avec plus de détails
      secureLog.info(`🔄 Auth change: ${event}`, {
        hasSession: !!session,
        hasUser: !!session?.user,
        userId: session?.user?.id || 'none'
      })

      setSession(session)
      setUser(session?.user || null)

      if (session?.user) {
        // Fetch profile in background
        fetchProfile(session.user.id)
      } else {
        setProfile(null)
      }
    })

    return () => {
      isMounted = false
      subscription.unsubscribe()
    }
  }, [])

  const fetchProfile = async (userId: string) => {
    // Check cache first (silencieux)
    if (profileCache[userId]) {
      secureLog.info('📋 Profile trouvé dans le cache', { userId })
      setProfile(profileCache[userId])
      return
    }

    secureLog.info('🔍 Récupération du profil...', { userId })

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        secureLog.error('❌ Profile query error', error)
        if (error.code === 'PGRST116') {
          secureLog.warn('⚠️ Profil non trouvé (PGRST116)', { userId })
          setProfile(null)
        }
        return
      }

      if (data) {
        // Cache and set profile (silencieux)
        secureLog.info('✅ Profil récupéré avec succès', {
          userId,
          role: data.role,
          firstName: data.first_name
        })
        setProfileCache(prev => ({ ...prev, [userId]: data }))
        setProfile(data)
      } else {
        secureLog.warn('⚠️ Aucune donnée de profil retournée', { userId })
        setProfile(null)
      }
    } catch (error) {
      secureLog.error('💥 Exception fetching profile', error)
    }
  }

  // Profile creation is now handled automatically by a SQL trigger
  const signUp = async (
    email: string,
    password: string,
    profileData?: Partial<Omit<UserProfile, 'id' | 'email' | 'role'>>
  ) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      })

      if (error) {
        secureLog.error('Signup error:', error)
        return { error }
      }

      return { error: null }
    } catch (error) {
      secureLog.error('Signup error:', error)
      return { error: error as AuthError }
    }
  }

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    return { error }
  }

  const signOut = async () => {
    try {
      // Clear local state immediately
      setUser(null)
      setProfile(null)
      setSession(null)

      // Sign out from Supabase
      const { error } = await supabase.auth.signOut()

      if (error) {
        secureLog.error('Sign out error:', error)
      }
    } catch (error) {
      secureLog.error('Sign out error:', error)
      // Force local sign out even on error
      setUser(null)
      setProfile(null)
      setSession(null)
    }
  }

  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      })
      return { error }
    } catch (error) {
      secureLog.error('Reset password error:', error)
      return { error: error as AuthError }
    }
  }

  const updatePassword = async (password: string) => {
    try {
      const { error } = await supabase.auth.updateUser({
        password: password
      })
      return { error }
    } catch (error) {
      secureLog.error('Update password error:', error)
      return { error: error as AuthError }
    }
  }

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user) {
      return { error: new Error('No user connected') }
    }

    try {
      const { error } = await supabase
        .from('profiles')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', user.id)

      if (error) {
        secureLog.error('Profile update error:', error)
        return { error }
      }

      // Update local profile
      setProfile(prev => {
        if (!prev) return null
        return { ...prev, ...updates }
      })

      return { error: null }
    } catch (error) {
      secureLog.error('Profile update error:', error)
      return { error }
    }
  }

  // Fonctions utilitaires pour vérifier les rôles
  const isAdmin = () => {
    return profile?.role === 'admin' || profile?.role === 'super_admin'
  }

  const isSuperAdmin = () => {
    return profile?.role === 'super_admin'
  }

  const hasRole = (role: 'user' | 'admin' | 'super_admin') => {
    return profile?.role === role
  }

  const value = {
    user,
    profile,
    session,
    loading,
    initialized,
    signUp,
    signIn,
    signOut,
    updateProfile,
    isAdmin,
    isSuperAdmin,
    hasRole,
    resetPassword,
    updatePassword,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
