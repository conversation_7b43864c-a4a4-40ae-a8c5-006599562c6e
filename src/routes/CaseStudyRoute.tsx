import React from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import CaseStudyPage from '../pages/CaseStudyPage';

const CaseStudyRoute: React.FC = () => {
  const { caseStudyId } = useParams<{ caseStudyId: string }>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const source = searchParams.get('source') as 'dashboard' | 'agenda' | 'assignments' || 'dashboard';

  const handleBack = () => {
    const sourceRoute = source === 'dashboard' ? '/dashboard' :
                       source === 'agenda' ? '/dashboard/agenda' : '/dashboard/assignments';
    navigate(sourceRoute);
  };

  return (
    <CaseStudyPage
      caseStudyId={caseStudyId || null}
      navigationSource={source}
      onBack={handleBack}
    />
  );
};

export default CaseStudyRoute;
