import React, { useState, Suspense, lazy } from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import { themes, subThemes, user as initialUser, modules } from '../data';
import { Theme, SubTheme, User, ConversationHistory } from '../types';
import Dashboard from '../pages/Dashboard';
import Sidebar from '../components/Sidebar';
import CookieConsent from '../components/CookieConsent';
import CookiePreferences from '../components/CookiePreferences';
import { useCookieConsent } from '../hooks/useCookieConsent';
import LoadingSpinner from '../components/LoadingSpinner';

// Lazy load des pages non-critiques pour réduire le bundle initial
const Library = lazy(() => import('../pages/Library'));
const Agenda = lazy(() => import('../pages/Agenda'));
const Assignments = lazy(() => import('../pages/Assignments'));
const Analytics = lazy(() => import('../pages/Analytics'));
const Profile = lazy(() => import('../pages/Profile'));
const ThemeRoute = lazy(() => import('./ThemeRoute'));
const ChatRoute = lazy(() => import('./ChatRoute'));
const CaseStudyRoute = lazy(() => import('./CaseStudyRoute'));
const ModuleRoute = lazy(() => import('./ModuleRoute'));

const AppLayout: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const [user, setUser] = useState<User>(initialUser);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [showPreferences, setShowPreferences] = useState(false);

  const { consent, loading, acceptAll, updatePreferences, resetConsent } = useCookieConsent();

  // Determine current view from URL
  const getCurrentView = () => {
    const path = location.pathname;
    if (path === '/dashboard' || path === '/dashboard/') return 'themes';
    if (path.startsWith('/dashboard/library')) return 'library';
    if (path.startsWith('/dashboard/agenda')) return 'journey';
    if (path.startsWith('/dashboard/assignments')) return 'notifications';
    if (path.startsWith('/dashboard/analytics')) return 'usage';
    if (path.startsWith('/dashboard/profile')) return 'profile';
    if (path.startsWith('/dashboard/theme/')) return 'subThemes';
    if (path.startsWith('/dashboard/chat/')) return 'chat';
    if (path.startsWith('/dashboard/case-study/')) return 'case-study';
    if (path.startsWith('/dashboard/module/')) return 'module';
    return 'themes';
  };

  const handleSelectTheme = (themeId: number) => {
    const theme = themes.find(t => t.id === themeId);
    if (theme && theme.isActive) {
      navigate(`/dashboard/theme/${theme.id}`);
    }
  };

  const handleSelectConversation = (conversation: ConversationHistory) => {
    const theme = themes.find(t => t.title === conversation.theme);
    const subTheme = subThemes.find(st => st.title === conversation.title);

    if (theme && subTheme) {
      navigate(`/dashboard/chat/${theme.id}/${subTheme.id}`);
    }
  };

  const handleUpdateUser = (updatedUser: User) => {
    setUser(updatedUser);
  };

  const handleNavigate = (view: string) => {
    const routeMap: { [key: string]: string } = {
      'themes': '/dashboard',
      'library': '/dashboard/library',
      'journey': '/dashboard/agenda',
      'notifications': '/dashboard/assignments',
      'usage': '/dashboard/analytics',
      'profile': '/dashboard/profile'
    };

    if (view === 'resources') {
      navigate('/dashboard');
    } else {
      navigate(routeMap[view] || '/dashboard');
    }
  };

  const handleOpenCaseStudy = (caseStudyId: string, source: 'dashboard' | 'agenda' | 'assignments' = 'dashboard') => {
    navigate(`/dashboard/case-study/${caseStudyId}?source=${source}`);
  };

  const handleOpenModule = (moduleId: string, source: 'dashboard' | 'agenda' | 'assignments' = 'dashboard') => {
    const module = modules.find(m => m.id === moduleId);
    if (module && module.status !== 'locked') {
      if (module.content?.caseStudyId) {
        handleOpenCaseStudy(module.content.caseStudyId, source);
      } else {
        navigate(`/dashboard/module/${moduleId}?source=${source}`);
      }
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar
        isCollapsed={isSidebarCollapsed}
        onToggle={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
        currentView={getCurrentView()}
        onNavigate={handleNavigate}
        onOpenProfile={() => navigate('/dashboard/profile')}
        onSelectConversation={handleSelectConversation}
      />

      <div className={`flex-1 transition-all duration-300 ${isSidebarCollapsed ? 'ml-12' : 'ml-64'}`}>
        <Suspense fallback={<LoadingSpinner />}>
          <Routes>
            {/* Routes relatives à partir de /dashboard ou /app */}
            <Route
              path="/"
              element={
                <Dashboard
                  themes={themes}
                  onSelectTheme={handleSelectTheme}
                  onOpenModule={(moduleId) => handleOpenModule(moduleId, 'dashboard')}
                />
              }
            />

            <Route
              path="library"
              element={
                <Library
                  themes={themes}
                  subThemes={subThemes}
                />
              }
            />

            <Route
              path="agenda"
              element={
                <Agenda
                  onOpenModule={(moduleId) => handleOpenModule(moduleId, 'agenda')}
                />
              }
            />

            <Route
              path="assignments"
              element={
                <Assignments
                  onOpenCaseStudy={(caseStudyId) => handleOpenCaseStudy(caseStudyId, 'assignments')}
                  onOpenModule={(moduleId) => handleOpenModule(moduleId, 'assignments')}
                />
              }
            />

            <Route path="analytics" element={<Analytics />} />

            <Route
              path="profile"
              element={
                <Profile />
              }
            />

            <Route path="theme/:themeId" element={<ThemeRoute />} />
            <Route path="chat/:themeId/:subThemeId" element={<ChatRoute />} />
            <Route path="case-study/:caseStudyId" element={<CaseStudyRoute />} />
            <Route path="module/:moduleId" element={<ModuleRoute />} />
          </Routes>
        </Suspense>
      </div>

      {/* Cookie consent components - only show in authenticated app */}
      {!loading && !consent && (
        <CookieConsent
          onAcceptAll={acceptAll}
          onCustomize={() => setShowPreferences(true)}
        />
      )}

      <CookiePreferences
        isOpen={showPreferences}
        onClose={() => setShowPreferences(false)}
        onSave={updatePreferences}
      />

      {/* Bouton de test pour les cookies (développement uniquement) */}
      {import.meta.env.DEV && (
        <button
          onClick={resetConsent}
          className="fixed top-4 right-4 bg-gray-800 text-white px-3 py-1 rounded text-xs z-50 opacity-50 hover:opacity-100 transition-opacity"
          title="Reset Cookie Consent (Dev only)"
        >
          🍪 Reset
        </button>
      )}
    </div>
  );
};

export default AppLayout;
