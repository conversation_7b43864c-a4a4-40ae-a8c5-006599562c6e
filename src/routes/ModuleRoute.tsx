import React from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import ModuleDetails from '../pages/ModuleDetails';

const ModuleRoute: React.FC = () => {
  const { moduleId } = useParams<{ moduleId: string }>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const source = searchParams.get('source') as 'dashboard' | 'agenda' | 'assignments' || 'dashboard';

  const handleBack = () => {
    const sourceRoute = source === 'dashboard' ? '/dashboard' :
                       source === 'agenda' ? '/dashboard/agenda' : '/dashboard/assignments';
    navigate(sourceRoute);
  };

  return (
    <ModuleDetails
      moduleId={moduleId || null}
      navigationSource={source}
      onBack={handleBack}
    />
  );
};

export default ModuleRoute;
