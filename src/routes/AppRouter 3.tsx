import React from 'react';
import { <PERSON>rowser<PERSON>outer, Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import { ConversationProvider } from '../contexts/ConversationContext';
import AuthWrapper from '../components/Auth/AuthWrapper';
import AppLayout from './AppLayout';
import ResetPassword from '../components/Auth/ResetPassword';
import LoginForm from '../components/Auth/LoginForm';
import SignUpForm from '../components/Auth/SignUpForm';
import ForgotPassword from '../components/Auth/ForgotPassword';

const AppRoutes: React.FC = () => {
  // Wrapper components qui gèrent la navigation (à l'intérieur du Router)
  const LoginPage: React.FC = () => {
    const navigate = useNavigate();
    return (
      <LoginForm
        onSwitchToSignUp={() => navigate('/sign-up')}
        onSwitchToForgotPassword={() => navigate('/forgot-password')}
      />
    );
  };

  const SignUpPage: React.FC = () => {
    const navigate = useNavigate();
    return (
      <SignUpForm onSwitchToLogin={() => navigate('/sign-in')} />
    );
  };

  const ForgotPasswordPage: React.FC = () => {
    const navigate = useNavigate();
    return (
      <ForgotPassword onBack={() => navigate('/sign-in')} />
    );
  };

  return (
    <Routes>
      {/* Public routes - no auth required */}
      <Route path="/sign-in" element={<LoginPage />} />
      <Route path="/sign-up" element={<SignUpPage />} />
      <Route path="/forgot-password" element={<ForgotPasswordPage />} />
      <Route path="/reset-password" element={<ResetPassword />} />

      {/* Redirect root to sign-in if not authenticated, otherwise to dashboard */}
      <Route path="/" element={<Navigate to="/sign-in" replace />} />

      {/* Protected routes - require auth */}
      <Route path="/dashboard/*" element={
        <AuthWrapper>
          <AppLayout />
        </AuthWrapper>
      } />

      <Route path="/app/*" element={
        <AuthWrapper>
          <AppLayout />
        </AuthWrapper>
      } />
    </Routes>
  );
};

const AppRouter: React.FC = () => {
  return (
    <BrowserRouter>
      <ConversationProvider>
        <AppRoutes />
      </ConversationProvider>
    </BrowserRouter>
  );
};

export default AppRouter;
