import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { themes, subThemes } from '../data';
import SubThemeSelection from '../pages/SubThemeSelection';

const ThemeRoute: React.FC = () => {
  const { themeId } = useParams<{ themeId: string }>();
  const navigate = useNavigate();

  const theme = themes.find(t => t.id === Number(themeId));

  if (!theme) {
    navigate('/dashboard');
    return null;
  }

  const handleSelectSubTheme = (subThemeId: number) => {
    const subTheme = subThemes.find(st => st.id === subThemeId);
    if (subTheme && subTheme.isActive) {
      navigate(`/chat/${theme.id}/${subTheme.id}`);
    }
  };

  return (
    <SubThemeSelection
      theme={theme}
      subThemes={subThemes}
      onSelectSubTheme={handleSelectSubTheme}
      onBack={() => navigate('/dashboard')}
    />
  );
};

export default ThemeRoute;
