import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { themes, subThemes } from '../data';
import ChatInterface from '../pages/ChatInterface';

const ChatRoute: React.FC = () => {
  const { themeId, subThemeId } = useParams<{ themeId: string; subThemeId: string }>();
  const navigate = useNavigate();

  const theme = themes.find(t => t.id === Number(themeId));
  const subTheme = subThemes.find(st => st.id === Number(subThemeId));

  if (!theme || !subTheme) {
    navigate('/dashboard');
    return null;
  }

  return (
    <ChatInterface
      theme={theme}
      subTheme={subTheme}
      onClose={() => navigate('/dashboard')}
    />
  );
};

export default ChatRoute;
