import { useState, useEffect } from 'react';
import { ConversationHistory, ChatMessage } from '../types';

const STORAGE_KEY = 'zest_conversation_history';
const MAX_CONVERSATIONS = 10;

export const useConversationHistory = () => {
  const [conversations, setConversations] = useState<ConversationHistory[]>(() => {
    const stored = localStorage.getItem(STORAGE_KEY);
    return stored ? JSON.parse(stored).map((conv: any) => ({
      ...conv,
      timestamp: new Date(conv.timestamp),
      messages: conv.messages.map((msg: any) => ({
        ...msg,
        timestamp: new Date(msg.timestamp)
      }))
    })) : [];
  });

  useEffect(() => {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(conversations));
  }, [conversations]);

  const addConversation = (theme: string, title: string, initialMessage: ChatMessage) => {
    const newConversation: ConversationHistory = {
      id: Date.now().toString(),
      theme,
      title,
      timestamp: new Date(),
      messages: [initialMessage]
    };

    setConversations(prev => {
      const updated = [newConversation, ...prev].slice(0, MAX_CONVERSATIONS);
      return updated;
    });

    return newConversation;
  };

  const addMessageToConversation = (conversationId: string, message: ChatMessage) => {
    setConversations(prev => prev.map(conv =>
      conv.id === conversationId
        ? { ...conv, messages: [...conv.messages, message], timestamp: new Date() }
        : conv
    ));
  };

  const deleteConversation = (conversationId: string) => {
    setConversations(prev => prev.filter(conv => conv.id !== conversationId));
  };

  const clearHistory = () => {
    setConversations([]);
  };

  return {
    conversations,
    addConversation,
    addMessageToConversation,
    deleteConversation,
    clearHistory
  };
};
