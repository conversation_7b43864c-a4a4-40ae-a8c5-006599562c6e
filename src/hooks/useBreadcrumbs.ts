import { useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { BreadcrumbItem } from '../components/Breadcrumbs';

export const useBreadcrumbs = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const breadcrumbs = useMemo((): BreadcrumbItem[] => {
    const path = location.pathname;
    const segments = path.split('/').filter(Boolean);

    // Default home breadcrumb
    const items: BreadcrumbItem[] = [];

    if (segments.length === 0 || path === '/dashboard') {
      return [{ label: 'Dashboard', isActive: true }];
    }

    // Map routes to breadcrumb labels
    const routeMap: { [key: string]: string } = {
      dashboard: 'Dashboard',
      library: 'Library',
      agenda: 'Agenda',
      assignments: 'Assignments',
      analytics: 'Analytics',
      profile: 'Profile',
      theme: 'Theme',
      chat: 'Chat',
      'case-study': 'Case Study',
      module: 'Module'
    };

    segments.forEach((segment, index) => {
      const isLast = index === segments.length - 1;
      const label = routeMap[segment] || segment;

      if (index === 0) {
        // First segment - always navigable unless it's the last
        items.push({
          label,
          onClick: isLast ? undefined : () => navigate(`/${segment}`),
          isActive: isLast
        });
      } else {
        // Subsequent segments
        items.push({
          label,
          onClick: isLast ? undefined : () => navigate(`/${segments.slice(0, index + 1).join('/')}`),
          isActive: isLast
        });
      }
    });

    return items;
  }, [location.pathname, navigate]);

  return breadcrumbs;
};

export default useBreadcrumbs;
