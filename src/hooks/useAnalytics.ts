import { useEffect, useState } from 'react';
import { useConversations } from '../contexts/ConversationContext';
import { KPI } from '../types';

const themeColors = {
  'Better understand and manage myself': '#E6CCE9',
  'Better understand my coworkers': '#C9CBEF',
  'Optimize team relationships': '#CDEEEF',
  'Maximize the individual performance of a collaborator': '#DFF2BF',
  'Maximize team performance': '#F5F9C2',
  'Contribute to strategy': '#FFE6B3',
  'Build a network of influence': '#F4C2C4'
};

const getThemeColor = (theme: string): string => {
  return themeColors[theme as keyof typeof themeColors] || '#6b7280';
};

export const useAnalytics = () => {
  const { conversations } = useConversations();
  const [analytics, setAnalytics] = useState<KPI>({
    interactions: 0,
    timeSpent: '20m',
    satisfaction: 4.5,
    themesUsage: [],
    conversationHistory: []
  });

  useEffect(() => {
    // Calculer le nombre total de messages utilisateur
    const userMessages = conversations.reduce((total, conv) => {
      return total + conv.messages.filter(msg => msg.sender === 'user').length;
    }, 0);

    // Calculer l'utilisation des thèmes basée sur le nombre de messages
    const themeUsage = new Map<string, number>();
    conversations.forEach(conv => {
      const messageCount = conv.messages.length;
      themeUsage.set(conv.theme, (themeUsage.get(conv.theme) || 0) + messageCount);
    });

    // Convertir en pourcentages
    const totalMessages = Array.from(themeUsage.values()).reduce((a, b) => a + b, 0) || 1;
    const themesUsageData = Array.from(themeUsage.entries()).map(([theme, count]) => ({
      theme,
      percentage: Math.round((count / totalMessages) * 100),
      color: getThemeColor(theme)
    }));

    setAnalytics({
      interactions: userMessages,
      timeSpent: '20m',
      satisfaction: 4.5,
      themesUsage: themesUsageData,
      conversationHistory: conversations
    });
  }, [conversations]);

  return analytics;
};
