import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { analytics } from '../utils/analytics';

export const useTracking = (trackingId?: string) => {
  const location = useLocation();

  useEffect(() => {
    // Initialiser Analytics au chargement de l'app
    if (trackingId) {
      analytics.initializeGoogleAnalytics(trackingId);
    }
  }, [trackingId]);

  useEffect(() => {
    // Tracker chaque changement de page
    analytics.trackPageView(location.pathname);
  }, [location.pathname]);

  return {
    // Fonctions helper pour les composants
    trackModuleUsage: analytics.trackModuleUsage.bind(analytics),
    trackChatInteraction: analytics.trackChatInteraction.bind(analytics),
    trackEvent: analytics.trackEvent.bind(analytics),
    canUseAnalytics: analytics.canUseAnalytics.bind(analytics)
  };
};
