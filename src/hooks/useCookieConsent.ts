import { useState, useEffect } from 'react';

const CONSENT_KEY = 'zest_cookie_consent';

interface CookieConsent {
  hasConsented: boolean;
  preferences: Record<string, boolean>;
}

export const useCookieConsent = () => {
  const [consent, setConsent] = useState<CookieConsent | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const storedConsent = localStorage.getItem(CONSENT_KEY);
    if (storedConsent) {
      setConsent(JSON.parse(storedConsent));
    }
    setLoading(false);
  }, []);

  const updateConsent = (newConsent: CookieConsent) => {
    localStorage.setItem(CONSENT_KEY, JSON.stringify(newConsent));
    setConsent(newConsent);
  };

  const acceptAll = () => {
    const allConsent: CookieConsent = {
      hasConsented: true,
      preferences: {
        essential: true,
        analytics: true,
        preferences: true
      }
    };
    updateConsent(allConsent);
  };

  const updatePreferences = (preferences: Record<string, boolean>) => {
    const updatedConsent: CookieConsent = {
      hasConsented: true,
      preferences
    };
    updateConsent(updatedConsent);
  };

  const resetConsent = () => {
    localStorage.removeItem(CONSENT_KEY);
    setConsent(null);
  };

  return {
    consent,
    loading,
    acceptAll,
    updatePreferences,
    resetConsent
  };
};
