import React from 'react';
import { User } from '../types';
import { FileIcon, DownloadIcon } from 'lucide-react';

interface PsychometricDataProps {
  user: User;
}

const PsychometricData: React.FC<PsychometricDataProps> = ({ user }) => {
  const psychometricTools = [
    { id: 'mbti', name: 'Myers-Briggs Type Indicator' },
    { id: 'pcm', name: 'Process Communication Model' },
    { id: 'driv', name: '<PERSON><PERSON><PERSON>' },
    { id: 'tki', name: 'Thomas-Kilmann Instrument' },
    { id: 'csi', name: 'Change Style Indicator' }
  ];

  const handleDownload = (toolId: string) => {
    if (user.reports?.[toolId as keyof typeof user.reports]) {
      // In a real app, this would trigger the actual download
      console.log(`Downloading ${toolId} report`);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-5 mb-6">
      <h3 className="text-lg font-medium mb-4">Psychometric Data</h3>
      
      <div className="space-y-4">
        {psychometricTools.map(tool => (
          <div key={tool.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <h4 className="text-sm font-medium text-gray-800">{tool.name}</h4>
            </div>
            {user.reports?.[tool.id as keyof typeof user.reports] ? (
              <button
                onClick={() => handleDownload(tool.id)}
                className="flex items-center text-[#8866a7] hover:text-[#775996] text-sm"
              >
                <DownloadIcon size={14} className="mr-1" />
                Download Report
              </button>
            ) : (
              <span className="text-xs text-gray-400 flex items-center">
                <FileIcon size={14} className="mr-1" />
                No report available
              </span>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default PsychometricData;