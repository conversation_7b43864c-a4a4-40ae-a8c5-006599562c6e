import React, { useState, useEffect } from 'react';
import { UserProfile as UserProfileType } from '../contexts/AuthContext';
import { generateAvatarUrl } from '../utils/avatar';
import Notification from './Notification';

interface InteractiveUserProfileProps {
  profile: UserProfileType;
  onUpdateProfile: (updates: Partial<UserProfileType>) => Promise<{ error: any | null }>;
}

const InteractiveUserProfile: React.FC<InteractiveUserProfileProps> = ({ profile, onUpdateProfile }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [editedProfile, setEditedProfile] = useState<UserProfileType>(profile);
  const [avatarUrl, setAvatarUrl] = useState(
    generateAvatarUrl(profile.first_name || 'U', profile.last_name || 'ser')
  );
  const [notification, setNotification] = useState<{
    type: 'success' | 'error';
    title: string;
    message: string;
    isVisible: boolean;
  }>({
    type: 'success',
    title: '',
    message: '',
    isVisible: false
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setEditedProfile(prev => ({ ...prev, [name]: value }));

    // Update avatar if first or last name changes
    if (name === 'first_name' || name === 'last_name') {
      const firstName = name === 'first_name' ? value : editedProfile.first_name;
      const lastName = name === 'last_name' ? value : editedProfile.last_name;
      setAvatarUrl(generateAvatarUrl(firstName || 'U', lastName || 'ser'));
    }
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      const { error } = await onUpdateProfile(editedProfile);

      if (error) {
        console.error('Update error:', error);
        setNotification({
          type: 'error',
          title: 'Profile update failed!',
          message: 'Please try again.',
          isVisible: true
        });
      } else {
        setNotification({
          type: 'success',
          title: 'Profile updated!',
          message: 'Your information has been successfully updated.',
          isVisible: true
        });
        setIsEditing(false);
      }
    } catch (error) {
      console.error('Update error:', error);
      setNotification({
        type: 'error',
        title: 'Error',
        message: 'An error occurred while updating your profile.',
        isVisible: true
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setEditedProfile(profile);
    setAvatarUrl(generateAvatarUrl(profile.first_name || 'U', profile.last_name || 'ser'));
    setIsEditing(false);
  };

  const generateCustomAvatar = () => {
    const newUrl = generateAvatarUrl(
      editedProfile.first_name || 'U',
      editedProfile.last_name || 'ser'
    );
    setAvatarUrl(newUrl);
  };

  // Sync profile changes from parent
  useEffect(() => {
    setEditedProfile(profile);
    setAvatarUrl(generateAvatarUrl(profile.first_name || 'U', profile.last_name || 'ser'));
  }, [profile]);

  return (
    <>
      <Notification
        type={notification.type}
        title={notification.title}
        message={notification.message}
        isVisible={notification.isVisible}
        onClose={() => setNotification(prev => ({ ...prev, isVisible: false }))}
      />

      <div className="bg-white rounded-lg shadow-md border border-gray-200">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <img
                  src={avatarUrl}
                  alt={`${editedProfile.first_name} ${editedProfile.last_name}`}
                  className="w-16 h-16 rounded-full border-2 border-gray-300"
                />
                {isEditing && (
                  <button
                    onClick={generateCustomAvatar}
                    className="absolute -bottom-1 -right-1 w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center hover:bg-gray-700 transition-colors"
                    title="Change Avatar"
                  >
                    <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                  </button>
                )}
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-800">Profile</h3>
                <p className="text-sm text-gray-600">
                  {profile.first_name !== 'To complete' && profile.last_name !== 'To complete'
                    ? `${profile.first_name} ${profile.last_name}`
                    : 'Complete your profile information'
                  }
                </p>
              </div>
            </div>

            {!isEditing ? (
              <button
                onClick={() => setIsEditing(true)}
                className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit
              </button>
            ) : (
              <div className="flex gap-2">
                <button
                  onClick={handleCancel}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  disabled={loading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                >
                  {loading ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Saving...
                    </div>
                  ) : (
                    'Save'
                  )}
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Profile form */}
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* First Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
              {isEditing ? (
                <input
                  type="text"
                  name="first_name"
                  value={editedProfile.first_name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your first name"
                />
              ) : (
                <div className="px-3 py-2 bg-gray-50 rounded-lg border border-gray-200">
                  {profile.first_name === 'To complete' ? (
                    <span className="text-gray-400 italic">To complete</span>
                  ) : (
                    <span className="text-gray-900">{profile.first_name}</span>
                  )}
                </div>
              )}
            </div>

            {/* Last Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
              {isEditing ? (
                <input
                  type="text"
                  name="last_name"
                  value={editedProfile.last_name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your last name"
                />
              ) : (
                <div className="px-3 py-2 bg-gray-50 rounded-lg border border-gray-200">
                  {profile.last_name === 'To complete' ? (
                    <span className="text-gray-400 italic">To complete</span>
                  ) : (
                    <span className="text-gray-900">{profile.last_name}</span>
                  )}
                </div>
              )}
            </div>

            {/* Profession */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Profession</label>
              {isEditing ? (
                <input
                  type="text"
                  name="profession"
                  value={editedProfile.profession}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your profession"
                />
              ) : (
                <div className="px-3 py-2 bg-gray-50 rounded-lg border border-gray-200">
                  {profile.profession === 'To complete' ? (
                    <span className="text-gray-400 italic">To complete</span>
                  ) : (
                    <span className="text-gray-900">{profile.profession}</span>
                  )}
                </div>
              )}
            </div>

            {/* Company */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Company</label>
              {isEditing ? (
                <input
                  type="text"
                  name="company_name"
                  value={editedProfile.company_name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your company name"
                />
              ) : (
                <div className="px-3 py-2 bg-gray-50 rounded-lg border border-gray-200">
                  {profile.company_name === 'To complete' ? (
                    <span className="text-gray-400 italic">To complete</span>
                  ) : (
                    <span className="text-gray-900">{profile.company_name}</span>
                  )}
                </div>
              )}
            </div>

            {/* Email - Full width */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
              <div className="px-3 py-2 bg-gray-50 rounded-lg border border-gray-200 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <span className="text-gray-900">{profile.email}</span>
                </div>
                <span className="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded">
                  Protected
                </span>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Email cannot be changed from this page for security reasons
              </p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default InteractiveUserProfile;
