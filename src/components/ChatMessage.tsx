import React from 'react';
import { ChatMessage as ChatMessageType } from '../types';
import ZestLogo from '/assets/zest-logo.png';
import ReactMarkdown from 'react-markdown';

interface ChatMessageProps {
  message: ChatMessageType;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
  const isUser = message.sender === 'user';
  
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className={`mb-6 flex ${isUser ? 'justify-end' : 'justify-start'}`}>
      <div className={`max-w-[85%] rounded-2xl p-4 ${
        isUser 
          ? 'bg-[#8866a7] text-white rounded-tr-none'
          : 'bg-white border border-gray-200 rounded-tl-none shadow-sm'
      }`}>
        <div className="flex items-start">
          {!isUser && (
            <div className="flex-shrink-0 mr-3">
              <img 
                src={ZestLogo} 
                alt="ZEST Logo" 
                className="w-8 h-8 rounded-full object-contain bg-white border border-gray-200"
              />
            </div>
          )}
          <div className={`flex-1 ${isUser ? 'text-right' : ''}`}>
            <div className={`text-xs mb-1 ${isUser ? 'text-purple-100' : 'text-gray-500'}`}>
              {isUser ? 'You' : 'ZEST Companion'} · {formatTime(message.timestamp)}
            </div>
            <div className={`prose ${isUser ? 'text-white' : 'text-gray-800'} max-w-none text-sm`}>
              <ReactMarkdown
                components={{
                  p: ({node, ...props}) => <p className="mb-4 last:mb-0" {...props} />,
                  ul: ({node, ...props}) => <ul className="mb-4 last:mb-0 list-disc pl-4" {...props} />,
                  ol: ({node, ...props}) => <ol className="mb-4 last:mb-0 list-decimal pl-4" {...props} />,
                  li: ({node, ...props}) => <li className="mb-1 last:mb-0" {...props} />,
                  strong: ({node, ...props}) => (
                    <strong className={`font-semibold ${isUser ? 'text-white' : 'text-gray-900'}`} {...props} />
                  ),
                  h3: ({node, ...props}) => (
                    <h3 className={`text-base font-semibold mb-2 ${isUser ? 'text-white' : 'text-gray-900'}`} {...props} />
                  ),
                  blockquote: ({node, ...props}) => (
                    <blockquote 
                      className={`border-l-4 pl-4 italic my-4 ${
                        isUser ? 'border-white/30 text-white/90' : 'border-gray-200 text-gray-600'
                      }`} 
                      {...props} 
                    />
                  ),
                }}
              >
                {message.content}
              </ReactMarkdown>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;