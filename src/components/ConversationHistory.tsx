import React, { useState } from 'react';
import { ConversationHistory } from '../types';
import { MessageSquareIcon, ClockIcon, ChevronDownIcon, ChevronUpIcon } from 'lucide-react';
import ChatMessage from './ChatMessage';

interface ConversationHistoryProps {
  conversations: ConversationHistory[];
}

const ConversationHistoryComponent: React.FC<ConversationHistoryProps> = ({ conversations }) => {
  const [expandedConversation, setExpandedConversation] = useState<string | null>(null);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const toggleConversation = (id: string) => {
    setExpandedConversation(expandedConversation === id ? null : id);
  };

  return (
    <div className="mt-8">
      <h4 className="text-sm font-medium text-gray-600 mb-3">Conversation History</h4>
      <div className="space-y-3">
        {conversations.map(conversation => (
          <div 
            key={conversation.id} 
            className="bg-gray-50 rounded-lg overflow-hidden transition-all duration-200 hover:shadow-md"
          >
            <div 
              className="p-4 cursor-pointer"
              onClick={() => toggleConversation(conversation.id)}
            >
              <div className="flex items-start justify-between">
                <div>
                  <h5 className="text-sm font-medium text-gray-800">{conversation.title}</h5>
                  <p className="text-xs text-gray-500 mt-1">{conversation.theme}</p>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex items-center text-xs text-gray-500">
                    <ClockIcon size={12} className="mr-1" />
                    {formatDate(conversation.timestamp)}
                  </div>
                  {expandedConversation === conversation.id ? (
                    <ChevronUpIcon size={16} className="text-gray-400" />
                  ) : (
                    <ChevronDownIcon size={16} className="text-gray-400" />
                  )}
                </div>
              </div>
              <div className="flex items-center mt-2 text-xs text-gray-500">
                <MessageSquareIcon size={12} className="mr-1" />
                {conversation.messages.length} messages
              </div>
            </div>

            {expandedConversation === conversation.id && (
              <div className="border-t border-gray-200 bg-white p-4">
                <div className="space-y-4">
                  {conversation.messages.map(message => (
                    <ChatMessage key={message.id} message={message} />
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ConversationHistoryComponent;