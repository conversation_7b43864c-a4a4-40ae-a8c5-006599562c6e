import React, { useState, useEffect } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { Lock, Eye, EyeOff, CheckIcon } from 'lucide-react'

const ResetPassword: React.FC = () => {
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  const { updatePassword } = useAuth()
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()

  useEffect(() => {
    // Vérifier si nous avons les paramètres nécessaires de Supabase
    const accessToken = searchParams.get('access_token')
    const refreshToken = searchParams.get('refresh_token')

    if (!accessToken || !refreshToken) {
      setError('Invalid or expired reset link. Please request a new link.')
    }
  }, [searchParams])

  const validatePassword = (pwd: string) => {
    if (pwd.length < 8) {
      return 'Password must be at least 8 characters long'
    }
    if (!/(?=.*[a-z])/.test(pwd)) {
      return 'Password must contain at least one lowercase letter'
    }
    if (!/(?=.*[A-Z])/.test(pwd)) {
      return 'Password must contain at least one uppercase letter'
    }
    if (!/(?=.*\d)/.test(pwd)) {
      return 'Password must contain at least one number'
    }
    return null
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    // Validation
    const passwordError = validatePassword(password)
    if (passwordError) {
      setError(passwordError)
      setLoading(false)
      return
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match')
      setLoading(false)
      return
    }

    const { error } = await updatePassword(password)

    if (error) {
      setError('Error updating password. The link may have expired.')
    } else {
      setSuccess(true)
      // Rediriger vers le dashboard après 3 secondes
      setTimeout(() => {
        navigate('/dashboard')
      }, 3000)
    }

    setLoading(false)
  }

  if (success) {
    return (
      <div className="min-h-screen flex">
        <div className="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24 bg-white min-h-screen">
          <div className="mx-auto w-full max-w-sm lg:w-96 pt-16">
            {/* Logo */}
            <div className="flex items-center gap-2 mb-8">
              <img
                src="/assets/zest-logo.png"
                alt="ZEST COMPANION Logo"
                className="w-8 h-8 object-contain"
              />
              <span className="text-xl text-gray-800 leading-none" style={{ letterSpacing: '1px' }}>
                <span className="font-bold">ZEST</span> <span className="text-gray-600">COMPANION</span>
              </span>
            </div>

            {/* Success Icon */}
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <CheckIcon className="w-8 h-8 text-green-600" />
              </div>
            </div>

            {/* Title */}
            <div className="text-center mb-8">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                Password updated!
              </h1>
              <p className="text-gray-600 text-sm">
                Your password has been successfully updated. You will be redirected to your dashboard.
              </p>
            </div>

            {/* Auto redirect message */}
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4 text-center">
              <div className="text-sm text-blue-800">
                Automatic redirect in a few seconds...
              </div>
            </div>
          </div>
        </div>

        {/* Partie droite */}
        <div className="hidden lg:block relative w-0 flex-1 bg-gradient-to-br from-green-600 to-green-800">
          <div className="absolute inset-0 flex flex-col justify-center px-12 text-white">
            <div className="mb-8 flex justify-center">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <CheckIcon className="w-8 h-8 text-white" />
              </div>
            </div>

            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">
                Success!
              </h2>
              <p className="text-green-100 text-lg">
                Your password has been<br />
                successfully updated.
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex">
      {/* Partie gauche - Formulaire */}
      <div className="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24 bg-white min-h-screen">
        <div className="mx-auto w-full max-w-sm lg:w-96 pt-16">
          {/* Logo */}
          <div className="flex items-center gap-2 mb-8">
            <img
              src="/assets/zest-logo.png"
              alt="ZEST COMPANION Logo"
              className="w-8 h-8 object-contain"
            />
            <span className="text-xl text-gray-800 leading-none" style={{ letterSpacing: '1px' }}>
              <span className="font-bold">ZEST</span> <span className="text-gray-600">COMPANION</span>
            </span>
          </div>

          {/* Title */}
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              New password
            </h1>
            <p className="text-gray-600 text-sm">
              Choose a new secure password for your account.
            </p>
          </div>

          {/* Formulaire */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
                {error}
              </div>
            )}

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                New password
              </label>
              <div className="mt-1 relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="appearance-none relative block w-full px-3 py-2 pl-10 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 focus:z-10 sm:text-sm"
                  placeholder="••••••••"
                />
                <Lock className="absolute left-3 top-2.5 h-4 w-4 text-gray-400 pointer-events-none z-20" />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center z-20"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </button>
              </div>
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                Confirm password
              </label>
              <div className="mt-1 relative">
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  required
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="appearance-none relative block w-full px-3 py-2 pl-10 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 focus:z-10 sm:text-sm"
                  placeholder="••••••••"
                />
                <Lock className="absolute left-3 top-2.5 h-4 w-4 text-gray-400 pointer-events-none z-20" />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center z-20"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </button>
              </div>
            </div>

            {/* Password requirements */}
            <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
              <div className="text-sm text-gray-600">
                <p className="font-medium mb-2">Your password must contain:</p>
                <ul className="list-disc list-inside space-y-1">
                  <li className={password.length >= 8 ? 'text-green-600' : ''}>
                    At least 8 characters
                  </li>
                  <li className={/(?=.*[a-z])/.test(password) ? 'text-green-600' : ''}>
                    One lowercase letter
                  </li>
                  <li className={/(?=.*[A-Z])/.test(password) ? 'text-green-600' : ''}>
                    One uppercase letter
                  </li>
                  <li className={/(?=.*\d)/.test(password) ? 'text-green-600' : ''}>
                    One number
                  </li>
                </ul>
              </div>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Updating...' : 'Update password'}
            </button>
          </form>
        </div>
      </div>

      {/* Partie droite - Présentation */}
      <div className="hidden lg:block relative w-0 flex-1 bg-gradient-to-br from-purple-600 to-purple-800">
        <div className="absolute inset-0 flex flex-col justify-center px-12 text-white">
          <div className="mb-8 flex justify-center">
            <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <Lock className="w-8 h-8 text-white" />
            </div>
          </div>

          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">
              Enhanced Security
            </h2>
            <p className="text-purple-100 text-lg">
              Create a strong password<br />
              to protect your account.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ResetPassword
