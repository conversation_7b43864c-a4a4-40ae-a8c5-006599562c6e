import React, { useState, useRef } from 'react'
import { Navigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import CompleteProfile from './CompleteProfile'
import { secureLog } from '../../utils/logger'

interface AuthWrapperProps {
  children: React.ReactNode
}

const AuthWrapper: React.FC<AuthWrapperProps> = ({ children }) => {
  const { user, profile, loading, initialized } = useAuth()
  const [profileWaitTime, setProfileWaitTime] = useState(0)
  const [initWaitTime, setInitWaitTime] = useState(0)
  const lastLoggedState = useRef<string>('')

  // Debug logs réduits (seulement pour les erreurs importantes)
  React.useEffect(() => {
    const currentState = `${initialized}-${!!user}-${!!profile}-${loading}`
    if (currentState !== lastLoggedState.current) {
      lastLoggedState.current = currentState

      // Log seulement les états problématiques
      if (!profile && profileWaitTime >= 5000) {
        secureLog.error('❌ AuthWrapper: Profile loading timeout')
      }
    }
  }, [initialized, user, profile, loading, profileWaitTime])

  // Count initialization wait time
  React.useEffect(() => {
    if (!initialized) {
      const timer = setInterval(() => {
        setInitWaitTime(prev => prev + 100)
      }, 100)

      return () => clearInterval(timer)
    } else {
      setInitWaitTime(0)
    }
  }, [initialized])

  // Count profile wait time only if necessary
  React.useEffect(() => {
    if (user && !profile) {
      const timer = setInterval(() => {
        setProfileWaitTime(prev => prev + 100)
      }, 100)

      return () => clearInterval(timer)
    } else {
      setProfileWaitTime(0)
    }
  }, [user, profile])

  // Show blank screen initially to prevent login flash, but with timeout
  if (!initialized) {
    if (initWaitTime < 3000) {
      return <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100"></div>
    } else {
      // After 3 seconds, show error message with retry option
      return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
          <div className="text-center bg-white p-6 rounded-lg shadow-md max-w-sm">
            <div className="text-orange-500 text-4xl mb-3">⚠️</div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Connection Issue</h3>
            <p className="text-gray-600 text-sm mb-4">
              Unable to connect to authentication service. Please check your internet connection.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded text-sm hover:bg-blue-700"
            >
              Retry Connection
            </button>
          </div>
        </div>
      )
    }
  }

  // Si l'utilisateur n'est pas connecté, rediriger vers le login
  if (!user) {
    return <Navigate to="/sign-in" replace />
  }

  // User exists but waiting for profile
  if (user && !profile && profileWaitTime < 5000) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center bg-white p-6 rounded-lg shadow-md max-w-sm">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Loading your profile...</h3>
          <p className="text-gray-600 text-sm">
            Please wait while we retrieve your information.
          </p>
        </div>
      </div>
    )
  }

  // Discrete error message only after 5 seconds (extreme case)
  if (user && !profile && profileWaitTime >= 5000) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center bg-white p-6 rounded-lg shadow-md max-w-sm">
          <div className="text-orange-500 text-4xl mb-3">⚠️</div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Profile Loading Error</h3>
          <p className="text-gray-600 text-sm mb-4">
            Unable to load your profile. This might be a connection issue.
          </p>
          <div className="space-y-2">
            <button
              onClick={() => window.location.reload()}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded text-sm hover:bg-blue-700"
            >
              Refresh Page
            </button>
            <button
              onClick={() => {
                localStorage.clear()
                window.location.href = '/sign-in'
              }}
              className="w-full bg-gray-600 text-white py-2 px-4 rounded text-sm hover:bg-gray-700"
            >
              Sign Out & Retry
            </button>
          </div>
        </div>
      </div>
    )
  }

  // If user is connected but profile is incomplete
  const isProfileIncomplete = profile && (
    profile.first_name === 'To complete' ||
    profile.last_name === 'To complete' ||
    profile.profession === 'To complete' ||
    profile.company_name === 'To complete'
  )

  if (isProfileIncomplete) {
    return <CompleteProfile />
  }

  // Profile is complete, show main app
  return <>{children}</>
}

export default AuthWrapper
