import React, { useState } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { ArrowLeftIcon, Mail, CheckIcon } from 'lucide-react'

interface ForgotPasswordProps {
  onBack: () => void
}

const ForgotPassword: React.FC<ForgotPasswordProps> = ({ onBack }) => {
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [emailSent, setEmailSent] = useState(false)

  const { resetPassword } = useAuth()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    const { error } = await resetPassword(email)

    if (error) {
      setError('An error occurred. Please check your email address and try again.')
    } else {
      setEmailSent(true)
    }

    setLoading(false)
  }

  if (emailSent) {
    return (
      <div className="min-h-screen flex">
        {/* Partie gauche - Confirmation */}
        <div className="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24 bg-white min-h-screen">
          <div className="mx-auto w-full max-w-sm lg:w-96 pt-16">
            {/* Logo */}
            <div className="flex items-center gap-2 mb-8">
              <img
                src="/assets/zest-logo.png"
                alt="ZEST COMPANION Logo"
                className="w-8 h-8 object-contain"
              />
              <span className="text-xl text-gray-800 leading-none" style={{ letterSpacing: '1px' }}>
                <span className="font-bold">ZEST</span> <span className="text-gray-600">COMPANION</span>
              </span>
            </div>

            {/* Success Icon */}
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <CheckIcon className="w-8 h-8 text-green-600" />
              </div>
            </div>

            {/* Title */}
            <div className="text-center mb-8">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                Email sent!
              </h1>
              <p className="text-gray-600 text-sm">
                We have sent a reset link to <strong>{email}</strong>.
                Check your inbox and click the link to set a new password.
              </p>
            </div>

            {/* Instructions */}
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-1">Instructions:</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>Check your inbox</li>
                  <li>Click the link in the email</li>
                  <li>Set your new password</li>
                </ul>
              </div>
            </div>

            {/* Actions */}
            <div className="space-y-3">
              <button
                onClick={() => handleSubmit({ preventDefault: () => {} } as React.FormEvent)}
                disabled={loading}
                className="w-full flex justify-center py-2.5 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"
              >
                {loading ? 'Sending...' : 'Resend email'}
              </button>

              <button
                onClick={onBack}
                className="w-full flex justify-center items-center py-2.5 px-4 text-sm font-medium text-purple-600 hover:text-purple-500"
              >
                <ArrowLeftIcon className="w-4 h-4 mr-2" />
                Back to sign in
              </button>
            </div>
          </div>
        </div>

        {/* Partie droite - Identique au LoginForm */}
        <div className="hidden lg:block relative w-0 flex-1 bg-gradient-to-br from-purple-600 to-purple-800">
          <div className="absolute inset-0 flex flex-col justify-center px-12 text-white">
            <div className="mb-8 flex justify-center">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <img
                  src="/assets/zest-logo.png"
                  alt="ZEST Logo"
                  className="w-8 h-8 object-contain"
                />
              </div>
            </div>

            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">
                Password<br />
                Recovery
              </h2>
              <p className="text-purple-100 text-lg">
                We help you regain access<br />
                to your account securely.
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex">
      {/* Partie gauche - Formulaire */}
      <div className="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24 bg-white min-h-screen">
        <div className="mx-auto w-full max-w-sm lg:w-96 pt-16">
          {/* Logo */}
          <div className="flex items-center gap-2 mb-8">
            <img
              src="/assets/zest-logo.png"
              alt="ZEST COMPANION Logo"
              className="w-8 h-8 object-contain"
            />
            <span className="text-xl text-gray-800 leading-none" style={{ letterSpacing: '1px' }}>
              <span className="font-bold">ZEST</span> <span className="text-gray-600">COMPANION</span>
            </span>
          </div>

          {/* Title */}
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Forgot password?
            </h1>
            <p className="text-gray-600 text-sm">
              Enter your email address and we'll send you a link to reset your password.
            </p>
          </div>

          {/* Formulaire */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
                {error}
              </div>
            )}

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <div className="mt-1 relative">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="appearance-none relative block w-full px-3 py-2 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 focus:z-10 sm:text-sm"
                  placeholder="Enter your email"
                />
                <Mail className="absolute left-3 top-2.5 h-4 w-4 text-gray-400 pointer-events-none z-20" />
              </div>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Sending...' : 'Send reset link'}
            </button>
          </form>

          {/* Back to login */}
          <button
            onClick={onBack}
            className="mt-6 w-full flex justify-center items-center text-sm text-purple-600 hover:text-purple-500"
          >
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            Back to sign in
          </button>
        </div>
      </div>

      {/* Partie droite - Présentation */}
      <div className="hidden lg:block relative w-0 flex-1 bg-gradient-to-br from-purple-600 to-purple-800">
        <div className="absolute inset-0 flex flex-col justify-center px-12 text-white">
          <div className="mb-8 flex justify-center">
            <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <img
                src="/assets/zest-logo.png"
                alt="ZEST Logo"
                className="w-8 h-8 object-contain"
              />
            </div>
          </div>

          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">
              Secure Recovery
            </h2>
            <p className="text-purple-100 text-lg">
              We help you regain access<br />
              to your account securely.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ForgotPassword
