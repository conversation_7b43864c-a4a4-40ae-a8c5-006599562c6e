import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import { supabase } from '../../lib/supabase'
import { Mail, Lock, UserPlus, AlertCircle, CheckCircle, Target, TrendingUp, Users } from 'lucide-react'
import { validateEmailWithDomain, getEmailSuggestions, getAllowedDomainsText } from '../../utils/emailValidation'
import { getSignUpErrorMessage } from '../../utils/errorMessages'
import { signupRateLimiter } from '../../utils/rateLimiter'
import { sanitizeEmail, sanitizeName, sanitizeString } from '../../utils/sanitization'

interface SignUpFormProps {
  onSwitchToLogin: () => void
}

const SignUpForm: React.FC<SignUpFormProps> = ({ onSwitchToLogin }) => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [emailValidation, setEmailValidation] = useState<{ isValid: boolean; error?: string } | null>(null)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [showLoginSuggestion, setShowLoginSuggestion] = useState(false)

  const { signUp, user } = useAuth()

  // Rediriger vers dashboard si déjà connecté
  useEffect(() => {
    if (user) {
      navigate('/dashboard', { replace: true });
    }
  }, [user, navigate]);

  const handleChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target

    // Sanitize input based on field type
    let sanitizedValue = value
    if (name === 'email') {
      sanitizedValue = sanitizeEmail(value)
    } else if (name === 'firstName' || name === 'lastName') {
      sanitizedValue = sanitizeName(value)
    } else if (name === 'profession' || name === 'companyName') {
      sanitizedValue = sanitizeString(value)
    }

    setFormData(prev => ({
      ...prev,
      [name]: sanitizedValue
    }))

    // Validation spéciale pour l'email
    if (name === 'email') {
      if (value.trim() === '') {
        setEmailValidation(null)
        setShowSuggestions(false)
      } else {
        setEmailValidation({ isValid: true }) // Temporary loading state
        try {
          const validation = await validateEmailWithDomain(value)
          setEmailValidation(validation)
        } catch (error) {
          console.error('Error validating email:', error)
          setEmailValidation({ isValid: false, error: 'Error validating email' })
        }
        setShowSuggestions(false) // Pas de suggestions sans nom/prénom
      }
    }
  }

  const validatePassword = (pwd: string) => {
    if (pwd.length < 8) {
      return 'Password must be at least 8 characters long'
    }
    if (!/(?=.*[a-z])/.test(pwd)) {
      return 'Password must contain at least one lowercase letter'
    }
    if (!/(?=.*[A-Z])/.test(pwd)) {
      return 'Password must contain at least one uppercase letter'
    }
    if (!/(?=.*\d)/.test(pwd)) {
      return 'Password must contain at least one number'
    }
    return null
  }

  const validateForm = async (): Promise<boolean> => {
    try {
      // Email validation with domain
      const emailValidation = await validateEmailWithDomain(formData.email)
      if (!emailValidation.isValid) {
        setError(emailValidation.error || 'Invalid email')
        return false
      }

      if (formData.password !== formData.confirmPassword) {
        setError('Passwords do not match')
        return false
      }

      // Enhanced password validation
      const passwordError = validatePassword(formData.password)
      if (passwordError) {
        setError(passwordError)
        return false
      }

      return true
    } catch (error) {
      console.error('Error validating form:', error)
      setError('Error validating email. Please try again.')
      return false
    }
  }

      // Check if email already exists in our database
  const checkEmailExists = async (email: string): Promise<boolean> => {
    try {
      console.log('🔍 Checking if email exists:', email)

      // Method 1: Try RPC function first
      try {
        const { data, error } = await supabase.rpc('check_email_exists', {
          email_to_check: email
        })

        if (!error) {
          console.log('📊 RPC Email check result:', data)
          return data === true
        }
        console.log('⚠️ RPC method failed, trying alternative:', error)
      } catch (rpcError) {
        console.log('⚠️ RPC method not available, trying alternative')
      }

      // Method 2: Alternative - check profiles table (which should mirror auth.users)
      const { data: profiles, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('email', email)
        .limit(1)

      if (profileError) {
        console.log('❌ Error checking profiles:', profileError)
        return false // If we can't check, proceed with signup
      }

      const exists = profiles && profiles.length > 0
      console.log('📊 Profiles check result:', exists)
      return exists

    } catch (error) {
      console.error('💥 Exception checking email:', error)
      return false // If we can't check, proceed with signup
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // ✅ Vérification rate limiting
    if (!signupRateLimiter.checkAttempt(formData.email)) {
      const remainingTime = signupRateLimiter.getRemainingTime(formData.email)
      const minutes = Math.floor(remainingTime / 60)
      const seconds = remainingTime % 60
      const timeText = minutes > 0 ? `${minutes}m ${seconds}s` : `${seconds}s`
      setError(`Too many signup attempts. Please wait ${timeText} before trying again.`)
      return
    }

    setError(null)
    setShowLoginSuggestion(false)

    const isValid = await validateForm()
    if (!isValid) {
      return
    }

    setLoading(true)

    console.log('🚀 SignUp: Starting signup with email:', formData.email)

    // TEMPORARY DEBUG: Force error for testing with specific email
    if (formData.email === '<EMAIL>') {
      console.log('🧪 DEBUG: Simulating "already exists" error')
      const debugError = {
        message: 'User already registered',
        code: 'user_already_exists',
        status: 422
      }

      const errorMessage = getSignUpErrorMessage(debugError)
      console.log('🔍 Debug processed error message:', errorMessage)
      setError(errorMessage)
      setShowLoginSuggestion(true)
      setLoading(false)
      return
    }

    // Note: Ne pas vérifier si l'email existe déjà pour éviter "user enumeration"
    // Laisser Supabase Auth gérer les doublons de manière sécurisée

    const { error } = await signUp(formData.email, formData.password, {})

    if (error) {
      console.error('🔍 Complete signup error:', error)

      // Gestion sécurisée des erreurs pour éviter user enumeration
      const message = error.message?.toLowerCase() || ''

      if (message.includes('already registered') ||
          message.includes('already exists') ||
          message.includes('duplicate') ||
          message.includes('already in use') ||
          error.status === 422) {

        // Message générique sécurisé - ne révèle pas si l'email existe
        setError('Si cette adresse email est nouvelle, vous recevrez un email de confirmation. Si vous avez déjà un compte, connectez-vous.')
        setShowLoginSuggestion(true)

      } else {
        // Autres erreurs (format, réseau, etc.)
        const errorMessage = getSignUpErrorMessage(error)
        setError(errorMessage)
      }
    } else {
      console.log('✅ Signup successful')
      // ✅ Réinitialiser le rate limiter après inscription réussie
      signupRateLimiter.reset(formData.email)
      setSuccess(true)
    }

    setLoading(false)
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-blue-100 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8 text-center">
          <div className="mx-auto h-16 w-16 bg-green-600 rounded-full flex items-center justify-center">
            <Mail className="h-8 w-8 text-white" />
          </div>
          <h2 className="text-3xl font-extrabold text-gray-900">
            Check your email!
          </h2>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-left">
            <h3 className="font-semibold text-blue-900 mb-2">Next steps:</h3>
            <ol className="text-sm text-blue-800 space-y-2">
              <li className="flex items-start">
                <span className="font-semibold mr-2">1.</span>
                <span>Check your email inbox for a confirmation message from ZEST Companion</span>
              </li>
              <li className="flex items-start">
                <span className="font-semibold mr-2">2.</span>
                <span>Click the confirmation link in the email</span>
              </li>
              <li className="flex items-start">
                <span className="font-semibold mr-2">3.</span>
                <span>Return here and sign in with your credentials</span>
              </li>
              <li className="flex items-start">
                <span className="font-semibold mr-2">4.</span>
                <span>Complete your profile to access the platform</span>
              </li>
            </ol>
          </div>
          <div className="text-sm text-gray-600">
            <p>Didn't receive the email? Check your spam folder or contact support.</p>
          </div>
          <button
            onClick={onSwitchToLogin}
            className="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Go to sign in
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex">
      {/* Partie gauche - Formulaire */}
      <div className="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24 bg-white min-h-screen">
        <div className="mx-auto w-full max-w-sm lg:w-96 pt-8">
          {/* Logo */}
          <div className="flex items-center gap-2 mb-8">
            <img
              src="/assets/zest-logo.png"
              alt="ZEST COMPANION Logo"
              className="w-8 h-8 object-contain"
            />
            <span className="text-xl text-gray-800 leading-none" style={{ letterSpacing: '1px' }}>
              <span className="font-bold">ZEST</span> <span className="text-gray-600">COMPANION</span>
            </span>
          </div>

          {/* Title */}
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Create your account
            </h1>
            <p className="text-gray-600 text-sm">
              Join ZEST Companion and start your leadership excellence journey.
            </p>
          </div>

          {/* Formulaire */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
                <div className="flex items-start">
                  <div className="flex-1">
                    {error}
                  </div>
                </div>
                {showLoginSuggestion && (
                  <div className="mt-3 pt-3 border-t border-red-200">
                                         <button
                       type="button"
                       onClick={onSwitchToLogin}
                       className="text-red-600 hover:text-red-800 font-medium underline"
                     >
                       Sign in with this account →
                     </button>
                  </div>
                )}
              </div>
            )}

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email
              </label>
              <div className="mt-1 relative">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  className={`appearance-none relative block w-full px-3 py-2 pl-10 pr-10 border placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:z-10 sm:text-sm ${
                    emailValidation === null
                      ? 'border-gray-300 focus:ring-purple-500 focus:border-purple-500'
                      : emailValidation.isValid
                      ? 'border-green-300 focus:ring-green-500 focus:border-green-500'
                      : 'border-red-300 focus:ring-red-500 focus:border-red-500'
                  }`}
                  placeholder="Enter your email"
                />
                <Mail className="absolute left-3 top-2.5 h-4 w-4 text-gray-400 pointer-events-none z-20" />

                {/* Icône de validation */}
                {emailValidation && (
                  <div className="absolute right-3 top-2.5">
                    {emailValidation.isValid ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                )}
              </div>

              {/* Message de validation */}
              {emailValidation && !emailValidation.isValid && formData.email.includes('@') && (
                <p className="mt-2 text-sm text-red-600 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  This email is not authorized.
                </p>
              )}


            </div>



            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div className="mt-1 relative">
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="new-password"
                  required
                  value={formData.password}
                  onChange={handleChange}
                  className="appearance-none relative block w-full px-3 py-2 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 focus:z-10 sm:text-sm"
                  placeholder="Minimum 8 characters"
                />
                <Lock className="absolute left-3 top-2.5 h-4 w-4 text-gray-400 pointer-events-none z-20" />
              </div>

              {/* Password requirements */}
              {formData.password && (
                <div className="mt-2 bg-gray-50 border border-gray-200 rounded-md p-3">
                  <div className="text-sm text-gray-600">
                    <p className="font-medium mb-2">Your password must contain:</p>
                    <ul className="list-disc list-inside space-y-1">
                      <li className={formData.password.length >= 8 ? 'text-green-600' : ''}>
                        At least 8 characters
                      </li>
                      <li className={/(?=.*[a-z])/.test(formData.password) ? 'text-green-600' : ''}>
                        One lowercase letter
                      </li>
                      <li className={/(?=.*[A-Z])/.test(formData.password) ? 'text-green-600' : ''}>
                        One uppercase letter
                      </li>
                      <li className={/(?=.*\d)/.test(formData.password) ? 'text-green-600' : ''}>
                        One number
                      </li>
                    </ul>
                  </div>
                </div>
              )}
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                Confirm Password
              </label>
              <div className="mt-1 relative">
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  autoComplete="new-password"
                  required
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  className="appearance-none relative block w-full px-3 py-2 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 focus:z-10 sm:text-sm"
                  placeholder="Repeat your password"
                />
                <Lock className="absolute left-3 top-2.5 h-4 w-4 text-gray-400 pointer-events-none z-20" />
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Creating account...' : 'Create account'}
              </button>
            </div>
          </form>

          {/* Login link */}
          <p className="mt-6 text-center text-sm text-gray-600">
            Already have an account?{' '}
            <button
              onClick={onSwitchToLogin}
              className="font-medium text-purple-600 hover:text-purple-500"
            >
              Sign in
            </button>
          </p>

          {/* Terms and Privacy notice */}
          <p className="mt-6 text-center text-xs text-gray-500 leading-relaxed">
            By creating an account, you agree to ZEST Companion's{' '}
            <a
              href="/terms-of-service"
              className="text-purple-600 hover:text-purple-500 underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              Terms of Service
            </a>
            {' '}and{' '}
            <a
              href="/privacy-policy"
              className="text-purple-600 hover:text-purple-500 underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              Privacy Policy
            </a>
            , and to receive periodic emails with updates.
          </p>
        </div>
      </div>

      {/* Right side - Presentation */}
      <div className="hidden lg:block relative w-0 flex-1 bg-gradient-to-br from-purple-600 to-purple-800">
        <div className="absolute inset-0 flex flex-col justify-center px-12 text-white">
          {/* Logo */}
          <div className="mb-8 flex justify-center">
            <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <img
                src="/assets/zest-logo.png"
                alt="ZEST Logo"
                className="w-8 h-8 object-contain"
              />
            </div>
          </div>

          {/* Main title */}
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">
              Welcome to<br />
              ZEST Companion
            </h2>
            <p className="text-purple-100 text-lg">
              Your learning and personal development<br />
              platform to excel in leadership.
            </p>
          </div>

          {/* Feature cards */}
          <div className="space-y-4 max-w-sm mx-auto">
            <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-4 border border-white border-opacity-20">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <Target className="h-6 w-6 text-orange-300" />
                </div>
                <div>
                  <h3 className="font-semibold text-white">Personalized Learning</h3>
                  <p className="text-purple-100 text-sm">
                    Develop your skills with modules adapted to your needs.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-4 border border-white border-opacity-20">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <TrendingUp className="h-6 w-6 text-green-300" />
                </div>
                <div>
                  <h3 className="font-semibold text-white">Progress Tracking</h3>
                  <p className="text-purple-100 text-sm">
                    Measure your progress and celebrate your achievements.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-4 border border-white border-opacity-20">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <Users className="h-6 w-6 text-yellow-300" />
                </div>
                <div>
                  <h3 className="font-semibold text-white">Team Collaboration</h3>
                  <p className="text-purple-100 text-sm">
                    Work together to achieve excellence.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SignUpForm
