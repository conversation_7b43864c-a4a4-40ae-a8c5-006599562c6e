import React from 'react';
import { Theme } from '../types';

interface ThemeCardProps {
  theme: Theme;
  onClick: (themeId: number) => void;
}

const ThemeCard: React.FC<ThemeCardProps> = ({ theme, onClick }) => {
  const borderColor = theme.isActive ? theme.color || '#000' : 'transparent';

  return (
    <div 
      className={`
        rounded-lg p-6 mb-4 cursor-pointer transition-all duration-300 transform hover:scale-[1.02]
        ${theme.isActive ? 'bg-white shadow-sm text-gray-800' : 'bg-gray-100 text-gray-400 hover:bg-gray-200'}
      `}
      onClick={() => theme.isActive && onClick(theme.id)}
      style={{
        borderTop: `4px solid ${borderColor}`
      }}
    >
      <div className="flex flex-col">
        <h3 className={`text-xl font-semibold mb-2`}>
          {theme.title}
        </h3>
        <p className={`text-sm`}>
          {theme.description}
        </p>
        {!theme.isActive && (
          <div className="mt-2 text-xs font-medium text-gray-400 flex items-center">
            <span className="bg-gray-200 px-2 py-1 rounded">Locked</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ThemeCard;