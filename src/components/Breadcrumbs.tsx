import React from 'react';
import { ChevronRightIcon, HomeIcon } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

export interface BreadcrumbItem {
  label: string;
  onClick?: () => void;
  isActive?: boolean;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
  className?: string;
}

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ items, className = '' }) => {
  const navigate = useNavigate();

  const handleDashboardClick = () => {
    navigate('/dashboard');
  };

  return (
    <nav className={`flex items-center space-x-1 text-sm ${className}`} aria-label="Breadcrumb">
      <button
        onClick={handleDashboardClick}
        className="flex items-center text-gray-500 hover:text-gray-700 transition-colors"
      >
        <HomeIcon size={14} className="mr-1" />
        <span>Dashboard</span>
      </button>

      {items.map((item, index) => (
        <React.Fragment key={index}>
          <ChevronRightIcon size={14} className="text-gray-400" />
          {item.onClick && !item.isActive ? (
            <button
              onClick={item.onClick}
              className="text-gray-500 hover:text-gray-700 transition-colors"
            >
              {item.label}
            </button>
          ) : (
            <span className={item.isActive ? 'text-gray-900 font-medium' : 'text-gray-500'}>
              {item.label}
            </span>
          )}
        </React.Fragment>
      ))}
    </nav>
  );
};

export default Breadcrumbs;
