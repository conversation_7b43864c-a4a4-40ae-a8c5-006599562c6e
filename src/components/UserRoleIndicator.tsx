import React from 'react'
import { useAuth } from '../contexts/AuthContext'
import { User, Shield, Crown } from 'lucide-react'

interface UserRoleIndicatorProps {
  showLabel?: boolean
  size?: 'sm' | 'md' | 'lg'
}

const UserRoleIndicator: React.FC<UserRoleIndicatorProps> = ({
  showLabel = true,
  size = 'md'
}) => {
  const { profile, isAdmin, isSuperAdmin } = useAuth()

  if (!profile?.role) {
    return null
  }

  const getRoleConfig = () => {
    switch (profile.role) {
      case 'super_admin':
        return {
          label: 'Super Admin',
          color: 'bg-purple-100 text-purple-800 border-purple-200',
          Icon: Crown
        }
      case 'admin':
        return {
          label: 'Admin',
          color: 'bg-blue-100 text-blue-800 border-blue-200',
          Icon: Shield
        }
      case 'user':
        return {
          label: 'User',
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          Icon: User
        }
      default:
        return {
          label: 'User',
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          Icon: User
        }
    }
  }

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-2 py-1 text-xs'
      case 'lg':
        return 'px-4 py-2 text-base'
      default:
        return 'px-3 py-1 text-sm'
    }
  }

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 12
      case 'lg':
        return 20
      default:
        return 16
    }
  }

  const roleConfig = getRoleConfig()
  const IconComponent = roleConfig.Icon

  return (
    <div className="flex items-center gap-2">
      <span
        className={`
          inline-flex items-center gap-1 rounded-full border font-medium
          ${roleConfig.color} ${getSizeClasses()}
        `}
      >
        <IconComponent size={getIconSize()} />
        {showLabel && <span>{roleConfig.label}</span>}
      </span>

      {/* Permission indicators */}
      {isAdmin() && (
        <div className="flex gap-1">
          <span
            className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 border border-green-200"
            title="Admin access"
          >
            ⚙️
          </span>
        </div>
      )}
    </div>
  )
}

export default UserRoleIndicator
