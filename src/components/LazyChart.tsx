import React, { lazy, Suspense } from 'react';
import LoadingSpinner from './LoadingSpinner';

const LazyPieChart = lazy(async () => {
  // Lazy load Chart.js seulement quand nécessaire
  const [reactChartJs2, chartJs] = await Promise.all([
    import('react-chartjs-2'),
    import('chart.js')
  ]);

  const { Pie } = reactChartJs2;
  const { Chart: ChartJS, ArcElement, Tooltip, Legend } = chartJs;

  ChartJS.register(ArcElement, Tooltip, Legend);

  return { default: Pie };
});

interface LazyChartProps {
  data: any;
  options: any;
}

const LazyChart: React.FC<LazyChartProps> = ({ data, options }) => {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <LazyPieChart data={data} options={options} />
    </Suspense>
  );
};

export default LazyChart;
