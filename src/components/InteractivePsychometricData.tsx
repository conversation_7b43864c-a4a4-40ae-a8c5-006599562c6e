import React, { useState } from 'react';
import { UserProfile } from '../contexts/AuthContext';

interface InteractivePsychometricDataProps {
  profile: UserProfile;
}

const InteractivePsychometricData: React.FC<InteractivePsychometricDataProps> = ({ profile }) => {
  const [downloadingTest, setDownloadingTest] = useState<string | null>(null);

  const psychometricTests = [
    {
      id: 'mbti',
      name: 'Myers-Briggs Type Indicator',
      description: 'Discover your personality type and work preferences',
      icon: '🎯',
      color: 'bg-blue-50 border-blue-200'
    },
    {
      id: 'pcm',
      name: 'Process Communication Model',
      description: 'Understand your communication and interaction style',
      icon: '💬',
      color: 'bg-green-50 border-green-200'
    },
    {
      id: 'tki',
      name: 'Thomas-Kilmann Conflict Mode',
      description: 'Assess your conflict management style',
      icon: '⚖️',
      color: 'bg-orange-50 border-orange-200'
    },
    {
      id: 'aci',
      name: 'Adaptability to Change Indicator',
      description: 'Identify your approach to change',
      icon: '🔄',
      color: 'bg-purple-50 border-purple-200'
    }
  ];

  const handleDownload = async (testId: string, testName: string) => {
    setDownloadingTest(testId);

    // Simulate report download
    console.log(`Downloading report: ${testName} for ${profile.first_name} ${profile.last_name}`);
    // Here you could implement the actual download logic

    setTimeout(() => {
      setDownloadingTest(null);
    }, 2000);
  };

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
            <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-800">Psychometric Data</h3>
            <p className="text-sm text-gray-600">
              Access your psychometric assessments and download your personalized reports
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {psychometricTests.map((test) => (
            <div
              key={test.id}
              className={`${test.color} border rounded-lg p-4 transition-all hover:shadow-md`}
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-start gap-3">
                  <div className="text-2xl">
                    {test.icon}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-800 mb-1">
                      {test.name}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {test.description}
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  onClick={() => handleDownload(test.id, test.name)}
                  disabled={downloadingTest === test.id}
                  className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50"
                >
                  {downloadingTest === test.id ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                      <span className="text-sm">Downloading...</span>
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <span className="text-sm">Download Report</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Personalized Reports Info */}
        <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707v11a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div className="flex-1">
              <h4 className="font-semibold text-gray-800 mb-1">Personalized Reports</h4>
              <p className="text-sm text-gray-600">
                All your reports are generated with your full name: <strong>{profile.first_name} {profile.last_name}</strong>
                {profile.company_name !== 'To complete' && (
                  <> from <strong>{profile.company_name}</strong></>
                )}
                . Data is automatically updated when you modify your profile.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InteractivePsychometricData;
