import React from 'react';
import { KPI } from '../types';
import { ClockIcon, MessageSquareIcon, BarChart3Icon, StarIcon } from 'lucide-react';
import Lazy<PERSON>hart from './LazyChart';

interface KPIDashboardProps {
  kpiData: KPI;
}

const KPIDashboard: React.FC<KPIDashboardProps> = ({ kpiData }) => {
  const pieData = {
    labels: kpiData.themesUsage.map(item => item.theme),
    datasets: [
      {
        data: kpiData.themesUsage.map(item => item.percentage),
        backgroundColor: kpiData.themesUsage.map(item => item.color),
        borderWidth: 0,
      },
    ],
  };

  const options = {
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          padding: 20,
          usePointStyle: true,
          pointStyle: 'circle',
        },
      },
    },
    cutout: '60%',
    maintainAspectRatio: false,
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-5">
      <h3 className="text-lg font-medium mb-4">Usage Analytics</h3>

      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="flex items-center mb-2">
            <MessageSquareIcon size={16} className="text-[#8866a7] mr-2" />
            <span className="text-sm text-gray-600">Interactions</span>
          </div>
          <p className="text-2xl font-semibold">{kpiData.interactions}</p>
        </div>

        <div className="bg-gray-50 rounded-lg p-3">
          <div className="flex items-center mb-2">
            <ClockIcon size={16} className="text-[#8866a7] mr-2" />
            <span className="text-sm text-gray-600">Time Spent</span>
          </div>
          <p className="text-2xl font-semibold">{kpiData.timeSpent}</p>
        </div>

        <div className="bg-gray-50 rounded-lg p-3">
          <div className="flex items-center mb-2">
            <StarIcon size={16} className="text-[#8866a7] mr-2" />
            <span className="text-sm text-gray-600">Satisfaction</span>
          </div>
          <p className="text-2xl font-semibold">{kpiData.satisfaction} <span className="text-sm text-gray-500">/ 5</span></p>
        </div>

        <div className="bg-gray-50 rounded-lg p-3">
          <div className="flex items-center mb-2">
            <BarChart3Icon size={16} className="text-[#8866a7] mr-2" />
            <span className="text-sm text-gray-600">Most Used</span>
          </div>
          <p className="text-lg font-semibold truncate">Team Performance</p>
        </div>
      </div>

      <div>
        <h4 className="text-sm font-medium text-gray-600 mb-3">Themes Usage</h4>
        <div className="h-64 relative">
          <LazyChart data={pieData} options={options} />
        </div>
      </div>
    </div>
  );
};

export default KPIDashboard;
