import React from 'react';
import { MessageSquareIcon, XIcon } from 'lucide-react';
import { ConversationHistory } from '../types';
import { useConversations } from '../contexts/ConversationContext';

interface SidebarChatHistoryProps {
  conversations: ConversationHistory[];
  isCollapsed: boolean;
  onSelectConversation: (conversation: ConversationHistory) => void;
}

const SidebarChatHistory: React.FC<SidebarChatHistoryProps> = ({
  conversations,
  isCollapsed,
  onSelectConversation
}) => {
  const { deleteConversation } = useConversations();

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  if (isCollapsed) {
    return null;
  }

  return (
    <div className="flex-1 overflow-y-auto">
      <div className="px-3 py-2">
        <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">
          Recent Conversations
        </h3>
        <div className="space-y-1">
          {conversations.map(conversation => (
            <div
              key={conversation.id}
              className="group relative"
            >
              <button
                onClick={() => onSelectConversation(conversation)}
              className="w-full text-left px-2 py-2 rounded-md transition-colors hover:bg-gray-100"
            >
              <div className="flex items-center min-w-0">
                <MessageSquareIcon size={14} className="text-gray-400 flex-shrink-0" />
                <div className="ml-3 min-w-0 flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-gray-700 truncate">
                      {conversation.title}
                    </span>
                    <span className="text-xs text-gray-500 ml-2 flex-shrink-0">
                      {formatDate(conversation.timestamp)}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500 truncate">
                    {conversation.theme}
                  </div>
                </div>
              </div>
            </button>
              <button
                onClick={() => deleteConversation(conversation.id)}
                className="absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded-full hover:bg-gray-200"
                title="Delete conversation"
              >
                <XIcon size={14} className="text-gray-400" />
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SidebarChatHistory;
