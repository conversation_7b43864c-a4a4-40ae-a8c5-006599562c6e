import React, { useState, useEffect } from 'react';
import { XIcon, DownloadIcon } from 'lucide-react';
import { CaseStudy, CaseStudyQuestion } from '../types';
import { jsPDF } from 'jspdf';

interface CaseStudyPanelProps {
  caseStudy: CaseStudy;
  onClose: () => void;
}

const CaseStudyPanel: React.FC<CaseStudyPanelProps> = ({ caseStudy, onClose }) => {
  const [questions, setQuestions] = useState<CaseStudyQuestion[]>(caseStudy.questions);
  const [hasAnswers, setHasAnswers] = useState(false);

  // Load saved answers from localStorage on mount
  useEffect(() => {
    const savedAnswers = localStorage.getItem(`caseStudy-${caseStudy.id}`);
    if (savedAnswers) {
      const parsedAnswers = JSON.parse(savedAnswers) as CaseStudyQuestion[];
      setQuestions(parsedAnswers);
      
      // Check if there's at least one answer
      const hasAtLeastOneAnswer = parsedAnswers.some(q => q.answer.trim() !== '');
      setHasAnswers(hasAtLeastOneAnswer);
    }
  }, [caseStudy.id]);

  // Save answers to localStorage whenever they change
  useEffect(() => {
    const hasAtLeastOneAnswer = questions.some(q => q.answer.trim() !== '');
    setHasAnswers(hasAtLeastOneAnswer);
    
    localStorage.setItem(`caseStudy-${caseStudy.id}`, JSON.stringify(questions));
  }, [questions, caseStudy.id]);

  const handleAnswerChange = (id: string, answer: string) => {
    setQuestions(prevQuestions => 
      prevQuestions.map(q => 
        q.id === id ? { ...q, answer } : q
      )
    );
  };

  const exportToPDF = () => {
    const doc = new jsPDF();
    
    // Add title
    doc.setFontSize(18);
    doc.text(caseStudy.title, 20, 20);
    
    // Add timestamp
    doc.setFontSize(10);
    const timestamp = new Date().toLocaleString();
    doc.text(`Generated on: ${timestamp}`, 20, 30);
    
    // Add context
    doc.setFontSize(12);
    doc.text("Context:", 20, 40);
    
    // Split context into multiple lines to fit page width
    const contextLines = doc.splitTextToSize(caseStudy.context, 170);
    doc.text(contextLines, 20, 45);
    
    let yPosition = 45 + contextLines.length * 7;
    
    // Add questions and answers
    questions.forEach((q, index) => {
      // Add question
      doc.setFontSize(12);
      doc.setFont(undefined, 'bold');
      const questionText = `Q${index + 1}: ${q.question}`;
      const questionLines = doc.splitTextToSize(questionText, 170);
      doc.text(questionLines, 20, yPosition);
      
      yPosition += questionLines.length * 7;
      
      // Add answer
      doc.setFont(undefined, 'normal');
      if (q.answer.trim()) {
        const answerLines = doc.splitTextToSize(q.answer, 170);
        doc.text(answerLines, 20, yPosition);
        yPosition += answerLines.length * 7 + 10;
      } else {
        doc.text("No answer provided", 20, yPosition);
        yPosition += 17;
      }
      
      // Check if we need a new page
      if (yPosition > 270) {
        doc.addPage();
        yPosition = 20;
      }
    });
    
    // Save the PDF
    doc.save(`${caseStudy.id}-answers.pdf`);
  };

  return (
    <div className="h-full flex flex-col bg-white border-l border-gray-200 overflow-hidden">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h2 className="text-lg font-semibold text-gray-800">Case Study</h2>
        <button 
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <XIcon size={20} />
        </button>
      </div>
      
      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-2">{caseStudy.title}</h3>
        <p className="text-sm text-gray-500 mb-6">{caseStudy.description}</p>
        
        <div className="bg-purple-50 p-4 rounded-lg mb-8">
          <h4 className="font-medium text-purple-800 mb-2">Context</h4>
          <p className="text-sm whitespace-pre-line">{caseStudy.context}</p>
        </div>
        
        <div className="space-y-8">
          {questions.map((q, index) => (
            <div key={q.id} className="border-b border-gray-100 pb-6">
              <h5 className="font-medium text-gray-800 mb-3">
                Question {index + 1}: {q.question}
              </h5>
              <textarea
                value={q.answer}
                onChange={(e) => handleAnswerChange(q.id, e.target.value)}
                placeholder="Type your answer here..."
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent min-h-[120px] text-sm"
              />
            </div>
          ))}
        </div>
      </div>
      
      {/* Footer with export button */}
      {hasAnswers && (
        <div className="px-6 py-4 border-t border-gray-200">
          <button 
            onClick={exportToPDF}
            className="flex items-center justify-center w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-md transition-colors"
          >
            <DownloadIcon size={16} className="mr-2" />
            Export as PDF
          </button>
        </div>
      )}
    </div>
  );
};

export default CaseStudyPanel;