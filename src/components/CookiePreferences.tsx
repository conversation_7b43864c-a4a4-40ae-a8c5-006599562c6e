import React, { useState } from 'react';
import { XIcon } from 'lucide-react';

interface CookiePreference {
  id: string;
  name: string;
  description: string;
  required: boolean;
}

interface CookiePreferencesProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (preferences: Record<string, boolean>) => void;
}

const CookiePreferences: React.FC<CookiePreferencesProps> = ({ isOpen, onClose, onSave }) => {
  const cookieTypes: CookiePreference[] = [
    {
      id: 'essential',
      name: 'Essential Cookies',
      description: 'Required for basic website functionality. Cannot be disabled.',
      required: true
    },
    {
      id: 'analytics',
      name: 'Analytics Cookies',
      description: 'Help us improve our services by collecting technical information.',
      required: false
    },
    {
      id: 'marketing',
      name: 'Marketing Cookies',
      description: 'Help us market our services more effectively to users.',
      required: false
    }
  ];

  const [preferences, setPreferences] = useState<Record<string, boolean>>(() => {
    return cookieTypes.reduce((acc, cookie) => ({
      ...acc,
      [cookie.id]: cookie.required
    }), {});
  });

  const handleToggle = (id: string) => {
    if (!cookieTypes.find(c => c.id === id)?.required) {
      setPreferences(prev => ({
        ...prev,
        [id]: !prev[id]
      }));
    }
  };

  const handleSave = () => {
    onSave(preferences);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4">
        <div className="p-6">
          <div className="flex items-start justify-between mb-6">
            <div>
              <h2 className="text-xl font-semibold text-gray-800">Cookie Preferences</h2>
              <p className="text-sm text-gray-600 mt-1">
                Manage your cookie preferences below
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XIcon size={20} />
            </button>
          </div>

          <div className="space-y-4 mb-6">
            {cookieTypes.map(cookie => (
              <div key={cookie.id} className="flex items-start space-x-4">
                <div className="pt-1">
                  <input
                    type="checkbox"
                    id={cookie.id}
                    checked={preferences[cookie.id]}
                    onChange={() => handleToggle(cookie.id)}
                    disabled={cookie.required}
                    className="rounded border-gray-300 text-[#8866a7] focus:ring-[#8866a7]"
                  />
                </div>
                <div>
                  <label 
                    htmlFor={cookie.id}
                    className="block text-sm font-medium text-gray-800"
                  >
                    {cookie.name}
                    {cookie.required && (
                      <span className="ml-2 text-xs text-gray-500">(Required)</span>
                    )}
                  </label>
                  <p className="text-sm text-gray-600 mt-1">
                    {cookie.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          <div className="flex justify-end space-x-4">
            <button
              onClick={onClose}
              className="text-gray-600 px-6 py-2 rounded-full text-sm font-medium hover:bg-gray-100 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="bg-[#8866a7] text-white px-6 py-2 rounded-full text-sm font-medium hover:bg-[#775996] transition-colors"
            >
              Save Preferences
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CookiePreferences;