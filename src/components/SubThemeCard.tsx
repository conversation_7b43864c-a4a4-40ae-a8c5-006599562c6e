import React from 'react';
import { SubTheme } from '../types';
import { LockIcon } from 'lucide-react';

const themeColors = {
  1: '#E6CCE9', // Better understand and manage myself (yellow)
  2: '#C9CBEF', // Better understand my coworkers (salmon)
  3: '#CDEEEF', // Optimize team relationships (peach)
  4: '#DFF2BF', // Maximize individual performance (lavender)
  5: '#F5F9C2', // Maximize team performance (green)
  6: '#FFE6B3', // Contribute to strategy (blue)
  7: '#F4C2C4', // Build a network of influence (light teal)
};

interface SubThemeCardProps {
  subTheme: SubTheme;
  onClick: (subThemeId: number) => void;
}

const SubThemeCard: React.FC<SubThemeCardProps> = ({ subTheme, onClick }) => {
  const themeId = Math.floor(subTheme.id / 100);
  const themeColor = themeColors[themeId as keyof typeof themeColors];

  return (
    <div 
      className={`
        bg-white border rounded-lg p-5 mb-3 transition-all duration-300
        ${subTheme.isActive 
          ? 'cursor-pointer hover:shadow-md' 
          : 'border-gray-100 cursor-not-allowed'
        }
      `}
      style={subTheme.isActive ? {
        borderLeft: `4px solid ${themeColor}`,
      } : undefined}
      onClick={() => subTheme.isActive && onClick(subTheme.id)}
    >
      <div className="flex items-start justify-between">
        <div className={`flex-1 ${!subTheme.isActive && 'opacity-50'}`}>
          <h3 className="text-lg font-medium text-gray-800 mb-1">
            {subTheme.title}
          </h3>
          <p className="text-sm text-gray-600">
            {subTheme.description}
          </p>
        </div>
        {!subTheme.isActive && (
          <div className="ml-4 flex items-center text-gray-400">
            <LockIcon size={16} className="mr-1" />
            <span className="text-xs font-medium">Locked</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default SubThemeCard;