import React from 'react';
import { ZapIcon } from 'lucide-react';

const ZestLogo: React.FC = () => {
  return (
    <div className="flex items-center">
      <div className="bg-[#8866a7] p-2 rounded-lg mr-2">
        <ZapIcon className="text-white" size={20} />
      </div>
      <div className="font-bold text-gray-800 tracking-tight">
        <span>ZEST</span>
        <span className="text-[#8866a7]"> Companion</span>
      </div>
    </div>
  );
};

export default ZestLogo;