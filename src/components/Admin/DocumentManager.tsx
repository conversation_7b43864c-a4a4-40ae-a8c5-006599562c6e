import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';
import {
  Upload, FileText, Play, Eye, EyeOff, Users, Plus, Trash2,
  Search, Filter, Calendar, UserPlus, UserMinus, Settings,
  Download, ExternalLink, Tag, FolderOpen, Clock
} from 'lucide-react';

interface Document {
  id: string;
  title: string;
  description: string;
  file_path: string;
  file_type: 'pdf' | 'video' | 'youtube' | 'image';
  file_size_bytes: number;
  theme_id: number;
  sub_theme_id: number;
  youtube_video_id?: string;
  tags: string[];
  is_public: boolean;
  created_at: string;
  created_by: string;
  access_count?: number;
}

interface UserAccess {
  id: string;
  user_id: string;
  user_email: string;
  user_name: string;
  granted_by: string;
  granted_at: string;
  expires_at?: string;
  is_active: boolean;
  notes?: string;
}

interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
}

const DocumentManager: React.FC = () => {
  const { profile } = useAuth();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [userAccesses, setUserAccesses] = useState<UserAccess[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [showUploadForm, setShowUploadForm] = useState(false);
  const [showAccessManager, setShowAccessManager] = useState(false);
  const [uploading, setUploading] = useState(false);

  // Formulaire de nouveau document
  const [newDocument, setNewDocument] = useState({
    title: '',
    description: '',
    file_type: 'pdf' as const,
    youtube_video_id: '',
    tags: '',
    is_public: false,
    theme_id: '',
    sub_theme_id: ''
  });

  // Formulaire d'accès utilisateur
  const [accessForm, setAccessForm] = useState({
    user_id: '',
    expires_at: '',
    notes: ''
  });

  useEffect(() => {
    loadDocuments();
    loadUsers();
  }, []);

  const loadDocuments = async () => {
    try {
      const { data, error } = await supabase
        .from('documents')
        .select(`
          *,
          profiles:created_by(first_name, last_name)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setDocuments(data || []);
    } catch (error) {
      console.error('Erreur lors du chargement des documents:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, email, first_name, last_name, role')
        .order('first_name');

      if (error) throw error;
      setUsers(data || []);
    } catch (error) {
      console.error('Erreur lors du chargement des utilisateurs:', error);
    }
  };

  const loadDocumentAccesses = async (documentId: string) => {
    try {
      const { data, error } = await supabase
        .from('user_document_access')
        .select(`
          *,
          profiles:user_id(email, first_name, last_name),
          granted_by_profile:granted_by(first_name, last_name)
        `)
        .eq('document_id', documentId)
        .eq('is_active', true);

      if (error) throw error;

      const formattedAccesses = data?.map(access => ({
        id: access.id,
        user_id: access.user_id,
        user_email: access.profiles?.email || '',
        user_name: `${access.profiles?.first_name} ${access.profiles?.last_name}`,
        granted_by: `${access.granted_by_profile?.first_name} ${access.granted_by_profile?.last_name}`,
        granted_at: access.granted_at,
        expires_at: access.expires_at,
        is_active: access.is_active,
        notes: access.notes
      })) || [];

      setUserAccesses(formattedAccesses);
    } catch (error) {
      console.error('Erreur lors du chargement des accès:', error);
    }
  };

  const handleUploadFile = async (file: File) => {
    setUploading(true);
    try {
      // Upload vers Supabase Storage
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(7)}.${fileExt}`;
      const filePath = `documents/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('documents')
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      // Créer l'entrée en base
      const { error: dbError } = await supabase
        .from('documents')
        .insert({
          title: newDocument.title,
          description: newDocument.description,
          file_path: filePath,
          file_type: newDocument.file_type,
          file_size_bytes: file.size,
          theme_id: newDocument.theme_id ? parseInt(newDocument.theme_id) : null,
          sub_theme_id: newDocument.sub_theme_id ? parseInt(newDocument.sub_theme_id) : null,
          tags: newDocument.tags.split(',').map(tag => tag.trim()).filter(Boolean),
          is_public: newDocument.is_public,
          created_by: profile?.id
        });

      if (dbError) throw dbError;

      // Reset form
      setNewDocument({
        title: '',
        description: '',
        file_type: 'pdf',
        youtube_video_id: '',
        tags: '',
        is_public: false,
        theme_id: '',
        sub_theme_id: ''
      });
      setShowUploadForm(false);
      await loadDocuments();

    } catch (error) {
      console.error('Erreur lors de l\'upload:', error);
    } finally {
      setUploading(false);
    }
  };

  const handleAddYouTubeVideo = async () => {
    try {
      const { error } = await supabase
        .from('documents')
        .insert({
          title: newDocument.title,
          description: newDocument.description,
          file_path: `https://youtube.com/watch?v=${newDocument.youtube_video_id}`,
          file_type: 'youtube',
          youtube_video_id: newDocument.youtube_video_id,
          theme_id: newDocument.theme_id ? parseInt(newDocument.theme_id) : null,
          sub_theme_id: newDocument.sub_theme_id ? parseInt(newDocument.sub_theme_id) : null,
          tags: newDocument.tags.split(',').map(tag => tag.trim()).filter(Boolean),
          is_public: newDocument.is_public,
          created_by: profile?.id
        });

      if (error) throw error;

      // Reset et reload
      setNewDocument({
        title: '',
        description: '',
        file_type: 'pdf',
        youtube_video_id: '',
        tags: '',
        is_public: false,
        theme_id: '',
        sub_theme_id: ''
      });
      setShowUploadForm(false);
      await loadDocuments();

    } catch (error) {
      console.error('Erreur lors de l\'ajout de la vidéo YouTube:', error);
    }
  };

  const handleGrantAccess = async () => {
    if (!selectedDocument || !accessForm.user_id) return;

    try {
      const { error } = await supabase.rpc('grant_document_access', {
        p_user_id: accessForm.user_id,
        p_document_id: selectedDocument.id,
        p_expires_at: accessForm.expires_at || null,
        p_notes: accessForm.notes || null
      });

      if (error) throw error;

      // Reset form et reload
      setAccessForm({ user_id: '', expires_at: '', notes: '' });
      await loadDocumentAccesses(selectedDocument.id);

    } catch (error) {
      console.error('Erreur lors de l\'octroi d\'accès:', error);
    }
  };

  const handleRevokeAccess = async (userId: string) => {
    if (!selectedDocument) return;

    try {
      const { error } = await supabase.rpc('revoke_document_access', {
        p_user_id: userId,
        p_document_id: selectedDocument.id
      });

      if (error) throw error;
      await loadDocumentAccesses(selectedDocument.id);

    } catch (error) {
      console.error('Erreur lors de la révocation d\'accès:', error);
    }
  };

  const toggleDocumentPublic = async (document: Document) => {
    try {
      const { error } = await supabase
        .from('documents')
        .update({ is_public: !document.is_public })
        .eq('id', document.id);

      if (error) throw error;
      await loadDocuments();

    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
    }
  };

  const deleteDocument = async (documentId: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce document ?')) return;

    try {
      const { error } = await supabase
        .from('documents')
        .delete()
        .eq('id', documentId);

      if (error) throw error;
      await loadDocuments();

    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case 'pdf': return <FileText size={16} className="text-red-500" />;
      case 'video': return <Play size={16} className="text-blue-500" />;
      case 'youtube': return <Play size={16} className="text-red-600" />;
      default: return <FileText size={16} className="text-gray-500" />;
    }
  };

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.tags?.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesType = filterType === 'all' || doc.file_type === filterType;

    return matchesSearch && matchesType;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">Gestion des Documents</h2>
          <p className="text-gray-600">Gérez l'accès aux documents pour vos utilisateurs</p>
        </div>
        <button
          onClick={() => setShowUploadForm(true)}
          className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
        >
          <Plus size={16} />
          Ajouter un document
        </button>
      </div>

      {/* Filtres et recherche */}
      <div className="flex gap-4 items-center">
        <div className="flex-1 relative">
          <Search size={16} className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Rechercher des documents..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
        </div>
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value)}
          className="px-4 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
        >
          <option value="all">Tous les types</option>
          <option value="pdf">PDF</option>
          <option value="video">Vidéo</option>
          <option value="youtube">YouTube</option>
        </select>
      </div>

      {/* Liste des documents */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Document</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Type</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Taille</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Statut</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Créé le</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredDocuments.map((document) => (
                <tr key={document.id} className="hover:bg-gray-50">
                  <td className="px-4 py-3">
                    <div className="flex items-start gap-3">
                      {getFileIcon(document.file_type)}
                      <div>
                        <div className="font-medium text-gray-900">{document.title}</div>
                        <div className="text-sm text-gray-500">{document.description}</div>
                        {document.tags && document.tags.length > 0 && (
                          <div className="flex gap-1 mt-1">
                            {document.tags.map((tag, index) => (
                              <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-700">
                                <Tag size={10} className="mr-1" />
                                {tag}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    <span className="text-sm text-gray-600 capitalize">{document.file_type}</span>
                  </td>
                  <td className="px-4 py-3">
                    <span className="text-sm text-gray-600">
                      {document.file_size_bytes ? formatFileSize(document.file_size_bytes) : '-'}
                    </span>
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex items-center gap-2">
                      {document.is_public ? (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-700">
                          <Eye size={10} className="mr-1" />
                          Public
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-700">
                          <EyeOff size={10} className="mr-1" />
                          Privé
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    <span className="text-sm text-gray-600">
                      {new Date(document.created_at).toLocaleDateString('fr-FR')}
                    </span>
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => toggleDocumentPublic(document)}
                        className="p-1 rounded-lg hover:bg-gray-100 transition-colors"
                        title={document.is_public ? 'Rendre privé' : 'Rendre public'}
                      >
                        {document.is_public ? <EyeOff size={16} className="text-gray-600" /> : <Eye size={16} className="text-gray-600" />}
                      </button>
                      <button
                        onClick={() => {
                          setSelectedDocument(document);
                          setShowAccessManager(true);
                          loadDocumentAccesses(document.id);
                        }}
                        className="p-1 rounded-lg hover:bg-gray-100 transition-colors"
                        title="Gérer les accès"
                      >
                        <Users size={16} className="text-gray-600" />
                      </button>
                      <button
                        onClick={() => deleteDocument(document.id)}
                        className="p-1 rounded-lg hover:bg-gray-100 transition-colors"
                        title="Supprimer"
                      >
                        <Trash2 size={16} className="text-red-600" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredDocuments.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            {searchTerm || filterType !== 'all' ? 'Aucun document trouvé avec ces filtres' : 'Aucun document ajouté'}
          </div>
        )}
      </div>

      {/* Modal d'ajout de document */}
      {showUploadForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-800">Ajouter un document</h3>
              <button
                onClick={() => setShowUploadForm(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Titre *</label>
                <input
                  type="text"
                  value={newDocument.title}
                  onChange={(e) => setNewDocument({ ...newDocument, title: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="Nom du document"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea
                  value={newDocument.description}
                  onChange={(e) => setNewDocument({ ...newDocument, description: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="Description du document"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Type de document</label>
                <select
                  value={newDocument.file_type}
                  onChange={(e) => setNewDocument({ ...newDocument, file_type: e.target.value as any })}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value="pdf">PDF</option>
                  <option value="video">Vidéo (upload)</option>
                  <option value="youtube">Vidéo YouTube</option>
                </select>
              </div>

              {newDocument.file_type === 'youtube' ? (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">ID Vidéo YouTube</label>
                  <input
                    type="text"
                    value={newDocument.youtube_video_id}
                    onChange={(e) => setNewDocument({ ...newDocument, youtube_video_id: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="dQw4w9WgXcQ"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    L'ID se trouve dans l'URL : youtube.com/watch?v=<strong>dQw4w9WgXcQ</strong>
                  </p>
                </div>
              ) : (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Fichier</label>
                  <input
                    type="file"
                    accept={newDocument.file_type === 'pdf' ? '.pdf' : 'video/*'}
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file && newDocument.title && newDocument.file_type !== 'youtube') {
                        handleUploadFile(file);
                      }
                    }}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  />
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tags (séparés par des virgules)</label>
                <input
                  type="text"
                  value={newDocument.tags}
                  onChange={(e) => setNewDocument({ ...newDocument, tags: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="leadership, formation, équipe"
                />
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="is_public"
                  checked={newDocument.is_public}
                  onChange={(e) => setNewDocument({ ...newDocument, is_public: e.target.checked })}
                  className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                />
                <label htmlFor="is_public" className="text-sm text-gray-700">
                  Document public (accessible à tous les utilisateurs)
                </label>
              </div>

              <div className="flex justify-end gap-3 pt-4">
                <button
                  onClick={() => setShowUploadForm(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Annuler
                </button>
                {newDocument.file_type === 'youtube' ? (
                  <button
                    onClick={handleAddYouTubeVideo}
                    disabled={!newDocument.title || !newDocument.youtube_video_id}
                    className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    Ajouter la vidéo
                  </button>
                ) : (
                  <span className="text-sm text-gray-500">Sélectionnez un fichier pour l'uploader</span>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de gestion des accès */}
      {showAccessManager && selectedDocument && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-800">
                Gestion des accès - {selectedDocument.title}
              </h3>
              <button
                onClick={() => setShowAccessManager(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>

            {/* Formulaire d'ajout d'accès */}
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <h4 className="font-medium text-gray-800 mb-3">Donner l'accès à un utilisateur</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Utilisateur</label>
                  <select
                    value={accessForm.user_id}
                    onChange={(e) => setAccessForm({ ...accessForm, user_id: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="">Sélectionner un utilisateur</option>
                    {users.map(user => (
                      <option key={user.id} value={user.id}>
                        {user.first_name} {user.last_name} ({user.email})
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Expiration (optionnel)</label>
                  <input
                    type="datetime-local"
                    value={accessForm.expires_at}
                    onChange={(e) => setAccessForm({ ...accessForm, expires_at: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  />
                </div>
              </div>
              <div className="mt-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">Notes (optionnel)</label>
                <input
                  type="text"
                  value={accessForm.notes}
                  onChange={(e) => setAccessForm({ ...accessForm, notes: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="Raison de l'accès..."
                />
              </div>
              <button
                onClick={handleGrantAccess}
                disabled={!accessForm.user_id}
                className="mt-3 flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <UserPlus size={16} />
                Donner l'accès
              </button>
            </div>

            {/* Liste des accès accordés */}
            <div>
              <h4 className="font-medium text-gray-800 mb-3">Utilisateurs ayant accès</h4>
              {userAccesses.length > 0 ? (
                <div className="space-y-3">
                  {userAccesses.map(access => (
                    <div key={access.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <div className="font-medium text-gray-800">{access.user_name}</div>
                        <div className="text-sm text-gray-600">{access.user_email}</div>
                        <div className="flex items-center gap-4 text-xs text-gray-500 mt-1">
                          <span>Accordé par {access.granted_by}</span>
                          <span>{new Date(access.granted_at).toLocaleDateString('fr-FR')}</span>
                          {access.expires_at && (
                            <span className="flex items-center gap-1">
                              <Clock size={10} />
                              Expire le {new Date(access.expires_at).toLocaleDateString('fr-FR')}
                            </span>
                          )}
                        </div>
                        {access.notes && (
                          <div className="text-xs text-gray-600 mt-1 italic">{access.notes}</div>
                        )}
                      </div>
                      <button
                        onClick={() => handleRevokeAccess(access.user_id)}
                        className="flex items-center gap-1 px-3 py-1 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      >
                        <UserMinus size={14} />
                        Révoquer
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  Aucun utilisateur n'a encore accès à ce document
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentManager;
