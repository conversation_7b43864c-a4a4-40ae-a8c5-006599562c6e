import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';
import { Plus, Trash2, Eye, EyeOff, Mail, Globe, Search, Filter, ShieldX } from 'lucide-react';

interface AllowedEmail {
  id: string;
  email_pattern: string;
  pattern_type: 'email' | 'domain';
  description: string | null;
  is_active: boolean;
  created_at: string;
}

const AllowedEmailsManager: React.FC = () => {
  const { isAdmin } = useAuth();
  const [emails, setEmails] = useState<AllowedEmail[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'email' | 'domain'>('all');
  const [filterActive, setFilterActive] = useState<'all' | 'active' | 'inactive'>('all');

  // Vérifier les permissions d'accès
  if (!isAdmin()) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="text-center py-8">
          <ShieldX className="mx-auto h-12 w-12 text-red-500 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
          <p className="text-red-600">
            You must be an administrator to access this section.
          </p>
        </div>
      </div>
    );
  }

  const [newEmail, setNewEmail] = useState({
    email_pattern: '',
    pattern_type: 'email' as 'email' | 'domain',
    description: ''
  });

  useEffect(() => {
    loadAllowedEmails();
  }, []);

  const loadAllowedEmails = async () => {
    try {
      const { data, error } = await supabase.rpc('get_allowed_emails');
      if (error) throw error;
      setEmails(data || []);
    } catch (error) {
      console.error('Loading error:', error);
    } finally {
      setLoading(false);
    }
  };

  const addAllowedEmail = async () => {
    if (!newEmail.email_pattern.trim()) return;

    try {
      const { error } = await supabase.rpc('add_allowed_email', {
        email_pattern: newEmail.email_pattern,
        pattern_type: newEmail.pattern_type,
        description: newEmail.description || null
      });

      if (error) throw error;

      setNewEmail({ email_pattern: '', pattern_type: 'email', description: '' });
      setShowAddForm(false);
      await loadAllowedEmails();
    } catch (error) {
      console.error('Erreur lors de l\'ajout:', error);
    }
  };

  const toggleEmailStatus = async (emailPattern: string, currentStatus: boolean) => {
    try {
      if (currentStatus) {
        // Désactiver
        await supabase.rpc('deactivate_allowed_email', {
          email_pattern: emailPattern
        });
      } else {
        // Réactiver (mise à jour directe)
        const { error } = await supabase
          .from('allowed_emails')
          .update({ is_active: true })
          .eq('email_pattern', emailPattern);

        if (error) throw error;
      }

      await loadAllowedEmails();
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
    }
  };

  const filteredEmails = emails.filter(email => {
    const matchesSearch = email.email_pattern.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (email.description?.toLowerCase().includes(searchTerm.toLowerCase()) || false);

    const matchesType = filterType === 'all' || email.pattern_type === filterType;
    const matchesActive = filterActive === 'all' ||
                         (filterActive === 'active' && email.is_active) ||
                         (filterActive === 'inactive' && !email.is_active);

    return matchesSearch && matchesType && matchesActive;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-800">Email Access Management</h2>
          <p className="text-gray-600 text-sm">Manage emails and domains authorized to access the application</p>
        </div>
        <button
          onClick={() => setShowAddForm(!showAddForm)}
          className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
        >
          <Plus size={16} className="mr-2" />
          Add
        </button>
      </div>

      {/* Formulaire d'ajout */}
      {showAddForm && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg border">
          <h3 className="font-medium text-gray-800 mb-4">Add a new access</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Type
              </label>
              <select
                value={newEmail.pattern_type}
                onChange={(e) => setNewEmail(prev => ({ ...prev, pattern_type: e.target.value as 'email' | 'domain' }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              >
                <option value="email">Specific Email</option>
                <option value="domain">Domain</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {newEmail.pattern_type === 'email' ? 'Email Address' : 'Domain Name'}
              </label>
              <input
                type="text"
                value={newEmail.email_pattern}
                onChange={(e) => setNewEmail(prev => ({ ...prev, email_pattern: e.target.value }))}
                placeholder={newEmail.pattern_type === 'email' ? '<EMAIL>' : 'example.com'}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description (optional)
              </label>
              <input
                type="text"
                value={newEmail.description}
                onChange={(e) => setNewEmail(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Description..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              />
            </div>
          </div>
          <div className="flex justify-end space-x-2 mt-4">
            <button
              onClick={() => setShowAddForm(false)}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={addAllowedEmail}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              Add
            </button>
          </div>
        </div>
      )}

      {/* Filtres et recherche */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          />
        </div>
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value as 'all' | 'email' | 'domain')}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
        >
          <option value="all">All types</option>
          <option value="email">Emails</option>
          <option value="domain">Domains</option>
        </select>
        <select
          value={filterActive}
          onChange={(e) => setFilterActive(e.target.value as 'all' | 'active' | 'inactive')}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
        >
          <option value="all">All statuses</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
      </div>

      {/* Liste des emails */}
      <div className="space-y-2">
        {filteredEmails.map((email) => (
          <div
            key={email.id}
            className={`flex items-center justify-between p-4 border rounded-lg transition-colors ${
              email.is_active
                ? 'border-gray-200 bg-white hover:bg-gray-50'
                : 'border-gray-200 bg-gray-50 opacity-75'
            }`}
          >
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                {email.pattern_type === 'email' ? (
                  <Mail className={`h-5 w-5 ${email.is_active ? 'text-blue-500' : 'text-gray-400'}`} />
                ) : (
                  <Globe className={`h-5 w-5 ${email.is_active ? 'text-green-500' : 'text-gray-400'}`} />
                )}
              </div>
              <div>
                <div className="flex items-center space-x-2">
                  <span className={`font-medium ${email.is_active ? 'text-gray-900' : 'text-gray-500'}`}>
                    {email.pattern_type === 'domain' && !email.email_pattern.startsWith('@') ? '@' : ''}
                    {email.email_pattern}
                  </span>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    email.pattern_type === 'email'
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-green-100 text-green-800'
                  }`}>
                    {email.pattern_type === 'email' ? 'Email' : 'Domain'}
                  </span>
                </div>
                {email.description && (
                  <p className="text-sm text-gray-500 mt-1">{email.description}</p>
                )}
                <p className="text-xs text-gray-400">
                  Added on {new Date(email.created_at).toLocaleDateString('en-US')}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => toggleEmailStatus(email.email_pattern, email.is_active)}
                className={`p-2 rounded-lg transition-colors ${
                  email.is_active
                    ? 'text-green-600 hover:bg-green-50'
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
                title={email.is_active ? 'Deactivate' : 'Activate'}
              >
                {email.is_active ? <Eye size={16} /> : <EyeOff size={16} />}
              </button>
            </div>
          </div>
        ))}

        {filteredEmails.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            {searchTerm || filterType !== 'all' || filterActive !== 'all'
              ? 'No results found with these filters'
              : 'No authorized email configured'
            }
          </div>
        )}
      </div>

      {/* Statistiques */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-purple-600">{emails.length}</div>
            <div className="text-sm text-gray-500">Total</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600">
              {emails.filter(e => e.is_active).length}
            </div>
            <div className="text-sm text-gray-500">Active</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-600">
              {emails.filter(e => e.pattern_type === 'email').length}
            </div>
            <div className="text-sm text-gray-500">Emails</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600">
              {emails.filter(e => e.pattern_type === 'domain').length}
            </div>
            <div className="text-sm text-gray-500">Domains</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AllowedEmailsManager;
