import React from 'react';
import { AlertCircleIcon, RefreshCwIcon } from 'lucide-react';

interface ErrorMessageProps {
  title?: string;
  message: string;
  onRetry?: () => void;
  className?: string;
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({
  title = 'Something went wrong',
  message,
  onRetry,
  className = ''
}) => {
  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-8 text-center ${className}`}>
      <AlertCircleIcon size={48} className="text-red-400 mx-auto mb-4" />
      <h2 className="text-xl font-semibold text-gray-800 mb-2">{title}</h2>
      <p className="text-gray-600 mb-6">{message}</p>
      {onRetry && (
        <button
          onClick={onRetry}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <RefreshCwIcon size={16} className="mr-2" />
          Try Again
        </button>
      )}
    </div>
  );
};

export default ErrorMessage;
