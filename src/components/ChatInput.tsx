import React, { useState } from 'react';
import { SendIcon } from 'lucide-react';

interface ChatInputProps {
  onSendMessage: (message: string) => void;
  selectedSubTheme?: number | null;
  disabled?: boolean;
}

const ChatInput: React.FC<ChatInputProps> = ({ onSendMessage, selectedSubTheme, disabled }) => {
  const [message, setMessage] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && selectedSubTheme && !disabled) {
      onSendMessage(message.trim());
      setMessage('');
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div className="flex items-stretch">
        <input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          disabled={!selectedSubTheme || disabled}
          placeholder={
            disabled ? "Ask about your leadership challenge..." :
            selectedSubTheme ? "Ask about your leadership challenge..." :
            "Select a sub-theme to start chatting..."
          }
          className="flex-1 h-12 border border-r-0 border-gray-200 rounded-l-full px-6 focus:outline-none focus:border-[#8866a7] disabled:bg-gray-50 disabled:text-gray-500"
        />
        <button
          type="submit"
          disabled={!message.trim() || !selectedSubTheme || disabled}
          className={`flex items-center justify-center h-12 border border-l-0 px-6 rounded-r-full transition-colors ${
            !message.trim() || !selectedSubTheme || disabled
              ? 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed'
              : 'bg-[#8866a7] border-[#8866a7] text-white hover:bg-[#775996] hover:border-[#775996]'
          }`}
        >
          <SendIcon size={18} />
        </button>
      </div>
    </form>
  );
};

export default ChatInput;
