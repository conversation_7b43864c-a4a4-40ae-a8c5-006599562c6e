import React, { useState } from 'react';
import { User } from '../types';
import { PencilIcon, CheckIcon, XIcon } from 'lucide-react';

interface UserProfileProps {
  user: User;
  onUpdateUser: (updatedUser: User) => void;
}

const UserProfile: React.FC<UserProfileProps> = ({ user, onUpdateUser }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedUser, setEditedUser] = useState<User>(user);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setEditedUser(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = () => {
    onUpdateUser(editedUser);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditedUser(user);
    setIsEditing(false);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-5 mb-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium">Profile</h3>
        {!isEditing ? (
          <button 
            onClick={() => setIsEditing(true)}
            className="text-[#8866a7] hover:text-[#775996] flex items-center text-sm"
          >
            <PencilIcon size={14} className="mr-1" />
            Edit
          </button>
        ) : (
          <div className="flex space-x-2">
            <button 
              onClick={handleSubmit}
              className="text-[#8866a7] hover:text-[#775996] flex items-center text-sm"
            >
              <CheckIcon size={14} className="mr-1" />
              Save
            </button>
            <button 
              onClick={handleCancel}
              className="text-gray-500 hover:text-gray-700 flex items-center text-sm"
            >
              <XIcon size={14} className="mr-1" />
              Cancel
            </button>
          </div>
        )}
      </div>

      <div className="flex flex-col md:flex-row">
        <div className="md:w-1/3 mb-4 md:mb-0 flex justify-center">
          <div className="relative">
            <img 
              src={editedUser.picture} 
              alt={`${editedUser.firstName} ${editedUser.lastName}`} 
              className="w-24 h-24 object-cover rounded-full border-4 border-[#8866a7]/20"
            />
            {isEditing && (
              <div className="absolute bottom-0 right-0">
                <label htmlFor="profilePic" className="bg-[#8866a7] text-white p-1 rounded-full cursor-pointer shadow-md">
                  <PencilIcon size={14} />
                  <input 
                    type="file" 
                    id="profilePic" 
                    className="hidden"
                    accept="image/*" 
                  />
                </label>
              </div>
            )}
          </div>
        </div>

        <div className="md:w-2/3">
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="block text-xs text-gray-500 mb-1">First Name</label>
              {isEditing ? (
                <input 
                  type="text" 
                  name="firstName"
                  value={editedUser.firstName}
                  onChange={handleChange}
                  className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
                />
              ) : (
                <p className="text-sm font-medium">{user.firstName}</p>
              )}
            </div>
            <div>
              <label className="block text-xs text-gray-500 mb-1">Last Name</label>
              {isEditing ? (
                <input 
                  type="text" 
                  name="lastName"
                  value={editedUser.lastName}
                  onChange={handleChange}
                  className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
                />
              ) : (
                <p className="text-sm font-medium">{user.lastName}</p>
              )}
            </div>
            <div>
              <label className="block text-xs text-gray-500 mb-1">Role</label>
              {isEditing ? (
                <input 
                  type="text" 
                  name="role"
                  value={editedUser.role}
                  onChange={handleChange}
                  className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
                />
              ) : (
                <p className="text-sm font-medium">{user.role}</p>
              )}
            </div>
            <div>
              <label className="block text-xs text-gray-500 mb-1">Department</label>
              {isEditing ? (
                <input 
                  type="text" 
                  name="department"
                  value={editedUser.department}
                  onChange={handleChange}
                  className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
                />
              ) : (
                <p className="text-sm font-medium">{user.department}</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfile;