import React, { useState } from 'react';
import { XIcon, CookieIcon } from 'lucide-react';

interface CookieConsentProps {
  onAcceptAll: () => void;
  onCustomize: () => void;
}

const CookieConsent: React.FC<CookieConsentProps> = ({ onAcceptAll, onCustomize }) => {
  const [isOpen, setIsOpen] = useState(true);
  const [isClosing, setIsClosing] = useState(false);

  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      setIsOpen(false);
    }, 300); // Durée de l'animation
  };

  const handleAcceptAll = () => {
    handleClose();
    setTimeout(() => {
      onAcceptAll();
    }, 300);
  };

  if (!isOpen) return null;

  return (
            <div
      className="fixed bottom-4 right-4 max-w-sm w-full sm:w-auto mx-4 sm:mx-0 bg-white border border-gray-200 rounded-lg shadow-xl z-50 p-4 transform transition-all duration-300 ease-out"
      style={{
        animation: isClosing ? 'slideOutDown 0.3s ease-out' : 'slideInUp 0.3s ease-out'
      }}
    >
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 mt-0.5">
          <div className="w-8 h-8 bg-[#8866a7] bg-opacity-10 rounded-full flex items-center justify-center">
            <CookieIcon size={16} className="text-[#8866a7]" />
          </div>
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between mb-2">
            <h3 className="text-sm font-medium text-gray-900">
              We use cookies
            </h3>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors ml-2 flex-shrink-0"
            >
              <XIcon size={16} />
            </button>
          </div>

          <p className="text-xs text-gray-600 mb-3 leading-relaxed">
            We use cookies to enhance your experience and analyze our traffic.
            <a
              href="/cookie-policy"
              className="text-[#8866a7] hover:underline ml-1"
            >
              Learn more
            </a>
          </p>

          <div className="flex gap-2">
            <button
              onClick={handleAcceptAll}
              className="flex-1 bg-[#8866a7] text-white px-3 py-2 rounded-md text-xs font-medium hover:bg-[#775996] hover:shadow-md transform hover:scale-105 transition-all duration-200"
            >
              Accept
            </button>
            <button
              onClick={onCustomize}
              className="flex-1 text-[#8866a7] border border-[#8866a7] px-3 py-2 rounded-md text-xs font-medium hover:bg-[#8866a7] hover:bg-opacity-10 hover:shadow-sm transform hover:scale-105 transition-all duration-200"
            >
              Customize
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CookieConsent;
