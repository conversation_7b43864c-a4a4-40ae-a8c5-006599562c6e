import React from 'react';
import { HomeIcon, BookOpenIcon, BellIcon, BarChartIcon, MenuIcon, CalendarIcon, LogOutIcon, Shield } from 'lucide-react';
import SidebarChatHistory from './SidebarChatHistory';
import { useConversations } from '../contexts/ConversationContext';
import { ConversationHistory } from '../types';
import { useAuth } from '../contexts/AuthContext'
import { generateAvatarUrl } from '../utils/avatar';

interface SidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
  currentView: string;
  onNavigate: (view: string) => void;
  onOpenProfile: () => void;
  onSelectConversation: (conversation: ConversationHistory) => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  isCollapsed,
  onToggle,
  currentView,
  onNavigate,
  onOpenProfile,
  onSelectConversation
}) => {
  const { conversations } = useConversations();
  const { profile, signOut, isAdmin } = useAuth();
  const [isSigningOut, setIsSigningOut] = React.useState(false);

  // Generate avatar URL like in the profile page
  const avatarUrl = React.useMemo(() => {
    return generateAvatarUrl(
      profile?.first_name || 'U',
      profile?.last_name || 'ser'
    );
  }, [profile?.first_name, profile?.last_name]);

  // Filtrer les conversations en double basées sur theme et title
  const uniqueConversations = conversations.reduce((acc, current) => {
    const isDuplicate = acc.find(
      conv => conv.theme === current.theme && conv.title === current.title
    );
    if (!isDuplicate) {
      acc.push(current);
    }
    return acc;
  }, [] as ConversationHistory[]);

  const navigationItems = [
    { id: 'dashboard', icon: HomeIcon, label: 'Dashboard', view: 'themes' },
    { id: 'library', icon: BookOpenIcon, label: 'Library', view: 'library' },
    { id: 'agenda', icon: CalendarIcon, label: 'Agenda', view: 'journey' },
    { id: 'assignments', icon: BellIcon, label: 'Assignments', view: 'notifications' },
    { id: 'analytics', icon: BarChartIcon, label: 'Analytics', view: 'usage' },
    ...(isAdmin() ? [{ id: 'admin', icon: Shield, label: 'Administration', view: 'admin' }] : []),
  ];

  return (
    <div
      className={`
        fixed left-0 top-0 h-screen bg-white border-r border-gray-200
        transition-all duration-300 z-20
        ${isCollapsed ? 'w-12' : 'w-64'}
      `}
    >
      <div className="h-20 px-3 flex items-center">
        <div className="flex items-center gap-2 translate-y-[2px]">
          <img
            src="/assets/zest-logo.png"
            alt="ZEST Companion Logo"
            className="w-6 h-6 object-contain"
          />
          {!isCollapsed && (
            <span className="text-xl text-gray-800 leading-none" style={{ letterSpacing: '1px' }}>
              <span className="font-bold">ZEST</span> COMPANION
            </span>
          )}
        </div>
      </div>

      <div className="flex flex-col h-[calc(100vh-5rem)]">
        <nav className="space-y-1 p-3">
          {navigationItems.map(item => {
            const Icon = item.icon;
            const isActive = currentView === item.view;
            return (
              <button
                key={item.id}
                onClick={() => onNavigate(item.view)}
                className={`
                  w-full flex items-center space-x-3 px-2 py-2 rounded-md transition-colors
                  ${isActive ? 'bg-[#f3eef7] text-[#8866a7]' : 'text-gray-600 hover:bg-gray-100'}
                  ${isCollapsed ? 'justify-center' : ''}
                `}
              >
                <Icon size={16} className="flex-shrink-0" />
                {!isCollapsed && <span className="text-sm font-medium">{item.label}</span>}
              </button>
            );
          })}
        </nav>

        <SidebarChatHistory
          conversations={uniqueConversations}
          isCollapsed={isCollapsed}
          onSelectConversation={onSelectConversation}
        />

        <div className="mt-auto p-3 space-y-2">
          <button
            onClick={onOpenProfile}
            className={`
              w-full flex items-center space-x-3 px-2 py-2 rounded-md
              text-gray-600 hover:bg-gray-100 transition-colors
              ${isCollapsed ? 'justify-center' : ''}
            `}
          >
            <img
              src={avatarUrl}
              alt={`${profile?.first_name || 'User'} ${profile?.last_name || ''}`}
              className="flex-shrink-0 w-4 h-4 rounded-full"
            />
            {!isCollapsed && (
              <div className="flex flex-col items-start min-w-0 flex-1">
                <span className="text-sm font-medium truncate">
                  {profile ? `${profile.first_name} ${profile.last_name}` : 'User'}
                </span>
                <span className="text-xs text-gray-500">
                  {profile?.profession !== 'To complete' ? profile?.profession : 'View Profile'}
                </span>
              </div>
            )}
          </button>

          <button
            onClick={async () => {
              setIsSigningOut(true);
              try {
                await signOut();
              } finally {
                setIsSigningOut(false);
              }
            }}
            disabled={isSigningOut}
            className={`
              w-full flex items-center space-x-3 px-2 py-2 rounded-md
              text-red-600 hover:bg-red-50 transition-colors
              ${isCollapsed ? 'justify-center' : ''}
              ${isSigningOut ? 'opacity-50 cursor-not-allowed' : ''}
            `}
            title="Sign Out"
          >
            {isSigningOut ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 flex-shrink-0" />
            ) : (
              <LogOutIcon size={16} className="flex-shrink-0" />
            )}
            {!isCollapsed && (
              <span className="text-sm font-medium">
                {isSigningOut ? 'Signing out...' : 'Sign Out'}
              </span>
            )}
          </button>
        </div>

        <button
          onClick={onToggle}
          className="
            absolute top-1/2 -right-3 w-6 h-6
            bg-white border border-gray-200 rounded-full
            flex items-center justify-center
            text-gray-400 hover:text-[#8866a7] transition-colors
            shadow-sm
          "
        >
          {isCollapsed ? (
            <MenuIcon size={14} className="ml-[1px]" />
          ) : (
            <MenuIcon size={14} className="mr-[1px]" />
          )}
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
