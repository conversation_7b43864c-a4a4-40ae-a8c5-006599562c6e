import { Theme, SubTheme, User, KPI, Notification, CaseStudy, CaseStudyQuestion, Module } from './types';


// PROFILE

export const user: User = {
  firstName: "<PERSON>",
  lastName: "<PERSON><PERSON>",
  role: "Marketing Manager",
  department: "Marketing",
  picture: "https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=600",
  reports: {
    mbti: "ESFJ",
    pcm: "Base/Phase Imaginer",
    driv: "Available",
    tki: "Available",
    csi: "Available"
  }
};

// THEME

export const themes: Theme[] = [
  {
    id: 1,
    title: "Better understand and manage myself",
    description: "Develop self-awareness and emotional intelligence skills",
    isActive: true,
    color: "#E6CCE9"
  },
  {
    id: 2,
    title: "Better understand my coworkers",
    description: "Learn how to better understand and collaborate with your team",
    isActive: true,
    color: "#C9CBEF"
  },
  {
    id: 3,
    title: "Optimize team relationships",
    description: "Improve communication and build stronger team connections",
    isActive: true,
    color: "#CDEEEF"
  },
  {
    id: 4,
    title: "Maximize the individual performance of a collaborator",
    description: "Help team members reach their full potential",
    isActive: true,
    color: "#DFF2BF"
  },
  {
    id: 5,
    title: "Maximize team performance",
    description: "Enhance overall team effectiveness and results",
    isActive: true,
    color: "#F5F9C2"
  },
  {
    id: 6,
    title: "Contribute to strategy",
    description: "Align team efforts with organizational goals and vision",
    isActive: true,
    color: "#FFE6B3"
  },
  {
    id: 7,
    title: "Build a network of influence",
    description: "Expand your impact through strategic relationships",
    isActive: true,
    color: "#F4C2C4"
  }
];

// SUB-THEMES

export const subThemes: SubTheme[] = [

  // Theme 1: Better understand and manage myself

    {
    id: 101,
    themeId: 1,
    title: "Explore my personality",
    description: "Discover how your MBTI® type shapes your behavior and reactions",
    isActive: true
  },

  {
    id: 102,
    themeId: 1,
    title: "Identify what drives or drains me",
    description: "Manage your motivation and energy",
    isActive: false
  },
   {
    id: 103,
    themeId: 1,
    title: "Clarify my leadership style",
    description: "Understand how you lead and how to grow",
    isActive: false
  },
   {
    id: 104,
    themeId: 1,
    title: "Define my version of success",
    description: "Align your goals with what truly matters to you",
    isActive: false
  },
   {
    id: 105,
    themeId: 1,
    title: "Manage my energy and avoid burnout",
    description: "Sustain your energy in demanding environments",
    isActive: false
  },
   {
    id: 106,
    themeId: 1,
    title: "Understand and manage my stress",
    description: "Recognize and respond to stress effectively",
    isActive: false
  },
   {
    id: 107,
    themeId: 1,
    title: "Connect to my core values",
    description: "Act in line with what's meaningful to you",
    isActive: false
  },
   {
    id: 108,
    themeId: 1,
    title: "Reframe my limiting beliefs",
    description: "Shift the beliefs that hold you back",
    isActive: false
  },

// Theme 2: Better understand my coworkers

  {
    id: 201,
    themeId: 2,
    title: "Explore their personality",
    description: "Understand how MBTI® types shape your coworkers' behavior",
    isActive: false
  },

  {
    id: 202,
    themeId: 2,
    title: "Identify what drives or drains them",
    description: "Understand what energizes or frustrates your coworkers",
    isActive: false
  },
   {
    id: 203,
    themeId: 2,
    title: "Understand their leadership style",
    description: "Adapt to different ways of leading and influencing",
    isActive: false
  },
   {
    id: 204,
    themeId: 2,
    title: "Clarify their definition of success",
    description: "Recognize what fulfillment means for your coworkers",
    isActive: false
  },
   {
    id: 205,
    themeId: 2,
    title: "Observe their energy and prevent burnout",
    description: "Spot early signs of fatigue and help others recharge",
    isActive: false
  },
   {
    id: 206,
    themeId: 2,
    title: "Understand their stress signals",
    description: "Decode how others react under pressure and respond better",
    isActive: false
  },
   {
    id: 207,
    themeId: 2,
    title: "Respect their values",
    description: "Navigate differences in values without conflict",
    isActive: false
  },
   {
    id: 208,
    themeId: 2,
    title: "Refcognize their limiting beliefs",
    description: "Identify what might be holding them back and support growth",
    isActive: false
  },

  // Theme 3 : Optimize team relationships

{
  id: 301,
  themeId: 3,
  title: "Build trust",
  description: "Foster psychological safety and rebuild trust after breakdowns",
  isActive: false
},
{
  id: 302,
  themeId: 3,
  title: "Manage and resolve conflicts",
  description: "Handle team tensions with clarity and emotional intelligence",
  isActive: false
},
{
  id: 303,
  themeId: 3,
  title: "Co-create a team charter",
  description: "Align on shared rules, roles, and collaboration principles",
  isActive: false
},
{
  id: 304,
  themeId: 3,
  title: "Practice active listening",
  description: "Strengthen connection and presence through deep listening",
  isActive: false
},
{
  id: 305,
  themeId: 3,
  title: "Improve team communication",
  description: "Communicate clearly and adapt to different styles",
  isActive: false
},
{
  id: 306,
  themeId: 3,
  title: "Handle difficult personalities",
  description: "Navigate tense behaviors with empathy and firmness",
  isActive: false
},

  // Theme 4 : Maximize the individual performance of a collaborator

{
  id: 401,
  themeId: 4,
  title: "Onboard a new team member",
  description: "Design smooth onboarding to foster ownership and integration",
  isActive: false
},
{
  id: 402,
  themeId: 4,
  title: "Clarify individual responsibilities",
  description: "Drive accountability with clear roles and ownership",
  isActive: false
},
{
  id: 403,
  themeId: 4,
  title: "Develop autonomy through coaching",
  description: "Unlock potential by adapting your coaching style",
  isActive: false
},
{
  id: 404,
  themeId: 4,
  title: "Support growth through mentoring",
  description: "Guide learning journeys with trust and dialogue",
  isActive: false
},
{
  id: 405,
  themeId: 4,
  title: "Conduct performance evaluations",
  description: "Use evaluations to drive insight and development",
  isActive: false
},
{
  id: 406,
  themeId: 4,
  title: "Give effective feedback",
  description: "Deliver feedback that builds clarity, trust, and growth",
  isActive: false
},

  // Theme 5 : Optimize team performance

  {
    id: 501,
    themeId: 5,
    title: "Recruit the right profiles",
    description: "Select candidates aligned with role, culture, and skills",
    isActive: false
  },
  {
    id: 502,
    themeId: 5,
    title: "Motivate the team",
    description: "Boost intrinsic and extrinsic engagement factors",
    isActive: true
  },
  {
    id: 503,
    themeId: 5,
    title: "Lead effective meetings",
    description: "Run focused, participative, and productive meetings",
    isActive: false
  },
  {
    id: 504,
    themeId: 5,
    title: "Improve priority management",
    description: "Align daily tasks with long-term goals",
    isActive: false
  },
  {
    id: 505,
    themeId: 5,
    title: "Optimize decision-making",
    description: "Make decisions collectively and efficiently",
    isActive: false
  },
  {
    id: 506,
    themeId: 5,
    title: "Focus on collective success",
    description: "Foster accountability and shared ambition",
    isActive: false
  },
  {
    id: 507,
    themeId: 5,
    title: "Facilitate a co-development workshop",
    description: "Use peer feedback to unlock team insights",
    isActive: false
  },
  {
    id: 508,
    themeId: 5,
    title: "Support transitions",
    description: "Help teams adapt during periods of change",
    isActive: false
  },
  {
    id: 509,
    themeId: 5,
    title: "Cultivate work-life balance",
    description: "Sustain engagement through healthy boundaries",
    isActive: false
  },

  // Theme 6 : Contribute to strategy

{
  id: 601,
  themeId: 6,
  title: "Clarify why our team matters",
  description: "Define your mission and align it with strategic priorities",
  isActive: false
},
{
  id: 602,
  themeId: 6,
  title: "Identify who we serve",
  description: "Map and prioritize customer segments that matter most",
  isActive: false
},
{
  id: 603,
  themeId: 6,
  title: "Craft a compelling value proposition",
  description: "Clarify what makes your offer stand out in the market",
  isActive: false
},
{
  id: 604,
  themeId: 6,
  title: "Execute strategy with focus",
  description: "Translate vision into action using tools and team alignment",
  isActive: false
},

  // Theme 7 : Build a network of influence

{
  id: 701,
  themeId: 7,
  title: "Assess my networking skills",
  description: "Clarify your style and opportunities to expand your network",
  isActive: false
},
{
  id: 702,
  themeId: 7,
  title: "Maximize intercultural awareness",
  description: "Adapt across cultures to build trust and impact globally",
  isActive: false
},
{
  id: 703,
  themeId: 7,
  title: "Practice positive influence techniques",
  description: "Communicate with impact, authenticity, and strategic intent",
  isActive: false
}
];

// ANALYTICS

export const kpiData: KPI = {
  interactions: 24,
  timeSpent: "3h 45m",
  themesUsage: [
    { theme: "Maximizing team performance", percentage: 45, color: "#8866a7" },
    { theme: "Understanding coworkers", percentage: 25, color: "#775996" },
    { theme: "Managing myself", percentage: 18, color: "#9b2f7a" },
    { theme: "Other themes", percentage: 12, color: "#6b7280" }
  ],
  satisfaction: 4.2,
  conversationHistory: [
    {
      id: "1",
      theme: "Maximizing team performance",
      title: "Team motivation strategies",
      timestamp: new Date("2024-02-20T14:30:00"),
      messages: [
        {
          id: "1",
          sender: "user",
          content: "How can I better motivate my team?",
          timestamp: new Date("2024-02-20T14:30:00")
        },
        {
          id: "2",
          sender: "assistant",
          content: "There are several effective approaches to team motivation...",
          timestamp: new Date("2024-02-20T14:31:00")
        }
      ]
    },
    {
      id: "2",
      theme: "Maximizing team performance",
      title: "Handling team burnout",
      timestamp: new Date("2024-02-19T10:15:00"),
      messages: [
        {
          id: "1",
          sender: "user",
          content: "My team seems burned out. What should I do?",
          timestamp: new Date("2024-02-19T10:15:00")
        },
        {
          id: "2",
          sender: "assistant",
          content: "Burnout is a serious concern that requires immediate attention...",
          timestamp: new Date("2024-02-19T10:16:00")
        }
      ]
    }
  ]
};

// TRAINING MODULES (AGENDA)

export const modules: Module[] = [
  {
    id: "module-1",
    type: "live_virtual",
    status: "upcoming",
    title: "Kick-off Workshop: MAPS 2025-2026",
    description: "Interactive session to launch the training journey, introduce participants, foster engagement, and explore personality facets through the MBTI framework.",
    date: new Date("2025-07-02T14:00:00"),
    duration: "4 hours",
    location: "Virtual",
    content: {
      objectives: [
        "Present the program, the trainers, and the participants",
        "Foster individual and collective commitment to action-oriented learning",
        "Understand personality facets to support personal and team development (MBTI part #1)"
      ],
      agenda: [
        "Program and Trainers Introduction",
        "Individual and Collective Commitment Ritual",
        "Discovering Personality Facets (MBTI Introduction)"
      ],
      prerequisites: [
        "Complete pre-workshop MBTI questionnaire",
        "Prepare a short personal introduction"
      ],
      materials: [
        "Kick-off slides",
        "MBTI type exploration guide",
        "Participant booklet"
      ]
    }
  },
  {
    id: "module-2",
    type: "interactive",
    status: "upcoming",
    title: "Translating Self-Awareness into Action",
    description: "Interactive case study designed to reflect on MBTI insights from the kick-off session and commit to a concrete learning posture before diving into deeper personality and leadership work.",
    date: new Date("2025-07-16T09:00:00"),
    duration: "45 minutes",
    content: {
      caseStudyId: "self-awareness-to-action"
    }
  },
  {
    id: "module-3",
    type: "live_in_person",
    status: "upcoming",
    title: "Leadership & Self-Alignment Intensive",
    description: "Two-day deep-dive to balance management and leadership postures, explore personality dynamics, values, drivers, and cultivate authentic presence and resilience.",
    date: new Date("2025-08-11T09:00:00"),
    duration: "2 days",
    location: "Main Conference Room",
    content: {
      objectives: [
        "Balance my postures as Manager and Leader",
        "Explore personality dynamics to grow and fuel myself (MBTI part #2)",
        "Identify and leverage my core values and strengths",
        "Understand what drives and drains my energy (DRiV part #1)",
        "Develop authentic presence rooted in who I am",
        "Recognize early signals of success and derailment",
        "Actively build resilience to become the best version of myself"
      ],
      agenda: [
        "Manager vs. Leader Postures",
        "Personality Dynamics & MBTI #2",
        "Values & Strengths Discovery",
        "DRiV Exploration & Energy Mapping",
        "Authentic Presence & Leadership Traps",
        "Resilience Practices & Closing Reflections"
      ],
      prerequisites: [
        "Complete MBTI and DRiV assessments",
        "Reflect on past experiences of success and failure"
      ],
      materials: [
        "2-day workshop slides",
        "MBTI deep-dive guide (part 2)",
        "DRiV profile & energy mapping worksheet",
        "Resilience toolbox",
        "Personal values and strengths grid"
      ]
    }
  },
  {
    id: "module-4",
    type: "interactive",
    status: "locked",
    title: "From Self to Team: Mapping Energy & Biases",
    description: "Reflection assignment to explore team energy dynamics using DRiV and uncover personal biases to foster inclusion.",
    date: new Date("2025-08-18T09:00:00"),
    duration: "1 hour"
  },
  {
    id: "module-5",
    type: "live_virtual",
    status: "locked",
    title: "Building High-Performance & Inclusive Teams",
    description: "Interactive workshop focused on the foundations of high-performing teams, energy dynamics, cultural differences, and inclusive leadership.",
    date: new Date("2025-09-08T09:00:00"),
    duration: "3.5 hours",
    location: "Virtual"
  },
  {
    id: "module-6",
    type: "live_in_person",
    status: "locked",
    title: "Deepening Interpersonal Intelligence & Team Charter Foundations",
    description: "Interactive in-person session focused on active listening, interpersonal insight, workplace well-being, and navigating complex team dynamics through practical tools and reflection.",
    date: new Date("2025-10-06T09:00:00"),
    duration: "2 days",
    location: "Executive Training Center"
  }
];


// ASSIGNMENTS

export const notifications: Notification[] = [
  {
    id: "1",
    title: "New assignment available",
    content: "Translating Self-Awareness into Action",
    type: "module",
    read: false,
    timestamp: new Date("2025-07-16T09:00:00"),
    moduleId: "module-2"
  },
  {
    id: "2",
    title: "Weekly reflection reminder",
    content: "Don't forget to complete your leadership journal entry",
    type: "reminder",
    read: true,
    timestamp: new Date("2024-03-14T15:00:00")
  }
];

export const caseStudies: CaseStudy[] = [
  {
    id: "self-awareness-to-action",
    title: "Translating Self-Awareness into Action",
    description: "Interactive case study designed to reflect on MBTI insights from the kick-off session and commit to a concrete learning posture before diving into deeper personality and leadership work.",
    context: `You are leading a new cross-functional team formed to pilot a high-visibility project across departments. The kick-off just took place, and while energy was high, several issues emerged:

- Some participants were talkative and dominant, while others held back
- A few team members seemed disengaged, distracted, or unclear about the purpose
- You noticed strong differences in how people react to pressure and decision-making
- The team's enthusiasm is high, but clarity, communication, and cohesion are still fragile

You’ve completed your MBTI profile and started to reflect on your own style. You’re now tasked with identifying how your personality affects the way you lead this team — and how to engage with others who have different preferences. The goal: to define intentional behaviors that will improve team connection and set a strong foundation for collaboration.`,

    questions: [
      {
        id: "q1",
        question: "Based on your MBTI profile, what strengths can you bring to this team as a leader?",
        answer: ""
      },
      {
        id: "q2",
        question: "Which behaviors or communication styles might you need to adjust to work more effectively with people who differ from you?",

        answer: ""
      },
      {
        id: "q3",
        question: "What clues or signals could help you detect disengagement early — and how might different personality types express it?",

        answer: ""
      },
      {
        id: "q4",
        question: "What will you commit to doing (or not doing) in the next module to contribute to a more inclusive, energized, and trust-based group dynamic?",

        answer: ""
      },
      {
        id: "q5",
        question: "If you had to define your 'leadership intention' in one sentence for the next step of the program, what would it be?",

        answer: ""
      }
    ]
  }
];
