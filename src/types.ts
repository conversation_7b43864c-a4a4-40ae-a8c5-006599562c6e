import { ReactNode } from 'react';

// Types de base pour l'interface utilisateur
export interface ThemeUI {
  id: number;
  title: string;
  description: string;
  isActive: boolean;
  color: string;
}

export interface SubTheme {
  id: number;
  themeId: number;
  title: string;
  description: string;
  isActive: boolean;
}

export interface User {
  firstName: string;
  lastName: string;
  role: string;
  department: string;
  picture: string;
  reports?: {
    mbti?: string;
    pcm?: string;
    driv?: string;
    tki?: string;
    csi?: string;
  };
}

export interface ChatMessage {
  id: string;
  sender: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

export interface ConversationHistory {
  id: string;
  theme: string;
  title: string;
  timestamp: Date;
  messages: ChatMessage[];
}

export interface KPI {
  interactions: number;
  timeSpent: string;
  themesUsage: {
    theme: string;
    percentage: number;
    color: string;
  }[];
  satisfaction: number;
  conversationHistory: ConversationHistory[];
}

export interface Notification {
  id: string;
  title: string;
  content: string;
  type: 'case_study' | 'update' | 'reminder' | 'module';
  read: boolean;
  timestamp: Date;
  moduleId?: string;
}

export interface CaseStudyQuestion {
  id: string;
  question: string;
  answer: string;
}

export interface CaseStudy {
  id: string;
  title: string;
  description: string;
  context: string;
  questions: CaseStudyQuestion[];
}

export type ModuleType = 'live_in_person' | 'live_virtual' | 'interactive';
export type ModuleStatus = 'upcoming' | 'completed' | 'locked';

export interface Module {
  id: string;
  type: ModuleType;
  status: ModuleStatus;
  title: string;
  description: string;
  date: Date;
  duration: string;
  location?: string;
  content?: {
    caseStudyId?: string;
    objectives?: string[];
    agenda?: string[];
    prerequisites?: string[];
    materials?: string[];
    [key: string]: any;
  };
}

// Type énuméré pour les rôles utilisateur (correspondant à l'ENUM PostgreSQL)
export type UserRole = 'user' | 'admin' | 'super_admin';

// Interface pour le profil utilisateur
export interface UserProfile {
  id: string;
  first_name: string;
  last_name: string;
  profession: string;
  company_name: string;
  email: string;
  role: UserRole;
  created_at: string;
  updated_at: string;
}

// Types pour l'authentification
export interface AuthContextType {
  user: any | null;
  profile: UserProfile | null;
  session: any | null;
  loading: boolean;
  initialized: boolean;
  signUp: (email: string, password: string, profileData?: Partial<Omit<UserProfile, 'id' | 'email' | 'role'>>) => Promise<{ error: any | null }>;
  signIn: (email: string, password: string) => Promise<{ error: any | null }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: any | null }>;
  updatePassword: (password: string) => Promise<{ error: any | null }>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<{ error: any | null }>;
  isAdmin: () => boolean;
  isSuperAdmin: () => boolean;
  hasRole: (role: UserRole) => boolean;
}

// Constantes pour les rôles (utile pour les comparaisons et la validation)
export const USER_ROLES = {
  USER: 'user' as const,
  ADMIN: 'admin' as const,
  SUPER_ADMIN: 'super_admin' as const,
} as const;

// Fonction utilitaire pour vérifier la validité d'un rôle
export function isValidUserRole(role: string): role is UserRole {
  return Object.values(USER_ROLES).includes(role as UserRole);
}

// Fonction utilitaire pour obtenir le libellé d'un rôle
export function getRoleLabel(role: UserRole): string {
  switch (role) {
    case USER_ROLES.SUPER_ADMIN:
      return 'Super Admin';
    case USER_ROLES.ADMIN:
      return 'Admin';
    case USER_ROLES.USER:
      return 'User';
    default:
      return 'User';
  }
}

// Types pour les emails autorisés
export interface AllowedEmail {
  id: string;
  email_pattern: string;
  pattern_type: 'email' | 'domain';
  description: string | null;
  is_active: boolean;
  created_at: string;
}

// Types pour les données psychométriques
export interface PsychometricData {
  [key: string]: any;
}

// Types pour les thèmes et modules (base de données)
export interface Theme {
  id: string;
  title: string;
  description: string;
  modules: Module[];
}

// Types pour l'historique des conversations
export interface Conversation {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  user_id: string;
}

export interface ChatMessageDB {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: string;
  conversation_id?: string;
}

// Types pour les assignements
export interface Assignment {
  id: string;
  title: string;
  description: string;
  context: string;
  instructions?: string;
  questions: AssignmentQuestion[];
  due_date?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface AssignmentQuestion {
  id: string;
  question: string;
  order_index: number;
}

export interface UserAssignment {
  id: string;
  assignment_id: string;
  user_id: string;
  assigned_by: string;
  assigned_at: string;
  due_date?: string;
  status: 'assigned' | 'in_progress' | 'completed';
  completed_at?: string;
}

export interface AssignmentResponse {
  id: string;
  user_assignment_id: string;
  question_id: string;
  answer: string;
  created_at: string;
  updated_at: string;
}

// Types étendus pour l'affichage
export interface AssignmentWithDetails extends Assignment {
  assigned_to?: UserProfile[];
  responses?: AssignmentResponse[];
  user_assignment?: UserAssignment;
}
