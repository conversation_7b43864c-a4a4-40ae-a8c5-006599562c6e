import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

// ✅ Enhanced environment validation
const validateEnvironment = () => {
  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Missing required Supabase environment variables')
  }

  // Validate URL format
  try {
    new URL(supabaseUrl)
  } catch {
    throw new Error('Invalid VITE_SUPABASE_URL format')
  }

  // Validate key format (basic check)
  if (supabaseAnonKey.length < 100) {
    throw new Error('Invalid VITE_SUPABASE_ANON_KEY format')
  }

  // Ensure we're using HTTPS in production
  if (import.meta.env.PROD && !supabaseUrl.startsWith('https://')) {
    throw new Error('Supabase URL must use HTTPS in production')
  }
}

// ✅ Logs sécurisés (sans données sensibles)
// if (process.env.NODE_ENV === 'development') {
//   console.log('🔧 Supabase Config:', {
//     hasUrl: !!supabaseUrl,
//     hasKey: !!supabaseAnonKey,
//     urlProtocol: supabaseUrl ? new URL(supabaseUrl).protocol : 'unknown'
//   })
// }

validateEnvironment()

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    // Configuration entreprise : sessions plus sécurisées
    storageKey: 'zest-companion-auth',
    storage: window.localStorage,
    // Enhanced security settings
    flowType: 'pkce', // Use PKCE flow for better security
    debug: false // Disable Supabase debug logs
  },
  // Global settings
  global: {
    headers: {
      'X-Client-Info': 'zest-companion-web'
    }
  }
})

export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          first_name: string
          last_name: string
          profession: string
          company_name: string
          email: string
          role: Database['public']['Enums']['user_role']
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          first_name: string
          last_name: string
          profession: string
          company_name: string
          email: string
          role?: Database['public']['Enums']['user_role']
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          first_name?: string
          last_name?: string
          profession?: string
          company_name?: string
          email?: string
          role?: Database['public']['Enums']['user_role']
          created_at?: string
          updated_at?: string
        }
      }
    }
    Enums: {
      user_role: 'user' | 'admin' | 'super_admin'
    }
  }
}
