# ZEST Companion

ZEST Companion est une plateforme de développement du leadership alimentée par l'IA qui fournit des conseils et un soutien personnalisés pour développer les compétences en leadership. L'application utilise l'analyse des types de personnalité MBTI pour adapter les conseils et recommandations au profil unique de chaque utilisateur.

## Fonctionnalités

- **Conversations Personnalisées** : Participez à des discussions contextuelles sur les défis de leadership
- **Apprentissage Thématique** : Explorez divers sujets de leadership organisés par thèmes
- **Intégration MBTI** : Recevez des conseils adaptés à votre type de personnalité MBTI
- **Suivi des Progrès** : Surveillez votre parcours d'apprentissage grâce aux analyses
- **Bibliothèque de Ressources** : Accédez à des matériaux sélectionnés pour le développement du leadership
- **Assignations Interactives** : Complétez des exercices pratiques et des études de cas
- **Système d'Authentification** : Connexion sécurisée avec gestion de profil
- **Gestion des Cookies** : Système de consentement conforme RGPD

## Stack Technique

- **Frontend** : React avec TypeScript, Vite, Tailwind CSS
- **Backend** : Supabase (Base de données PostgreSQL + Auth + API)
- **Routing** : React Router DOM avec structure d'URL moderne
- **Analyses** : Chart.js pour les visualisations
- **Icons** : Lucide React
- **Build** : Vite avec optimisations bundle

## Structure des URLs

### Routes Publiques
- `/` - Redirection vers sign-in
- `/sign-in` - Page de connexion
- `/sign-up` - Page d'inscription
- `/forgot-password` - Mot de passe oublié
- `/reset-password` - Réinitialisation mot de passe

### Routes Protégées (nécessitent authentification)
- `/dashboard` - Tableau de bord principal
- `/dashboard/library` - Bibliothèque de ressources
- `/dashboard/profile` - Profil utilisateur
- `/dashboard/analytics` - Analyses et statistiques
- `/dashboard/agenda` - Planning et planification
- `/dashboard/assignments` - Assignations et exercices

## Prise en Main

1. Cloner le repository
```bash
git clone https://github.com/hillzeealex/zest-companion.git
cd zest-companion
```

2. Installer les dépendances
```bash
npm install
```

3. Configuration des variables d'environnement
Créer un fichier `.env.local` dans le répertoire racine :
```
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. Démarrer le serveur de développement
```bash
npm run dev
```

## Dépannage

### Problème : "Connection Issue" lors du démarrage

Si vous rencontrez un message "Connection Issue" au démarrage de l'application, cela peut être dû à un conflit de port.

**Symptômes :**
- Message d'erreur "Unable to connect to authentication service"
- L'application reste bloquée sur l'écran de connexion
- Les WebSockets ne se connectent pas correctement

**Solution :**

1. **Vérifier si le port est déjà utilisé :**
```bash
lsof -i :5173
```

2. **Si une autre instance utilise le port, la tuer :**
```bash
# Remplacez PID par le numéro du processus affiché
kill [PID]
```

3. **Ou laisser Vite choisir automatiquement un autre port :**
Vite détectera automatiquement que le port 5173 est occupé et utilisera le port suivant disponible (ex: 5174).

4. **Forcer un port spécifique (optionnel) :**
Modifiez votre `package.json` :
```json
{
  "scripts": {
    "dev": "vite --port 5174"
  }
}
```

**Prévention :**
- Fermez toujours proprement votre serveur de développement avec `Ctrl+C`
- Vérifiez les processus en arrière-plan avant de redémarrer

### Problème : Variables d'environnement manquantes

**Symptômes :**
- Erreur "Les variables d'environnement Supabase sont manquantes"
- L'application ne se connecte pas à Supabase

**Solution :**
Vérifiez que votre fichier `.env.local` contient :
```
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Configuration du Debug/Logging

Le projet utilise un système de logging sécurisé qui s'adapte à l'environnement :

### Mode Production
- **Silence complet** : Aucun log affiché en production pour la sécurité
- **Protection des données** : Aucune fuite d'informations sensibles (IDs utilisateur, emails)

### Mode Développement
Le logging peut être contrôlé via le fichier `src/utils/logger.ts` :

```typescript
// Mode silencieux : même en dev, on peut vouloir moins de logs
const isQuietMode = false; // Mettre à true pour réduire les logs
```

**Options disponibles :**
- `isQuietMode = false` : Logs détaillés pour le debugging (par défaut)
- `isQuietMode = true` : Mode silencieux même en développement

### Types de logs disponibles
```typescript
import { secureLog } from '../utils/logger';

secureLog.info('Message d\'information');
secureLog.warn('Message d\'avertissement');
secureLog.error('Message d\'erreur'); // Toujours affiché
secureLog.important('Message important'); // Affiché même en mode quiet
```

## Configuration Supabase

Le projet utilise Supabase pour :
- **Authentification** : Gestion des utilisateurs avec email/mot de passe
- **Base de données** : Stockage des profils utilisateur et données
- **API** : Endpoints automatiques pour les opérations CRUD
- **Sécurité** : Row Level Security (RLS) pour la protection des données

## Structure du Projet

```
src/
├── components/          # Composants React réutilisables
│   ├── Auth/           # Composants d'authentification
│   ├── Admin/          # Composants d'administration
│   └── ...             # Autres composants UI
├── pages/              # Pages principales de l'application
├── routes/             # Configuration du routage
├── contexts/           # Contextes React (Auth, Conversation)
├── hooks/              # Hooks React personnalisés
├── utils/              # Fonctions utilitaires
├── lib/                # Configuration des librairies (Supabase)
└── types.ts            # Définitions TypeScript
```

## Fonctionnalités Avancées

### Système de Cookies
- Consentement RGPD avec choix granulaire
- Cookies essentiels, analytiques et marketing
- Stockage local des préférences

### Analyses et Tracking
- Suivi des pages et interactions
- Statistiques d'utilisation anonymisées
- Tableaux de bord avec Chart.js

### Optimisations Performance
- Bundle splitting automatique
- Lazy loading des composants
- Taille optimisée : 77KB (main chunk)

## Scripts Disponibles

```bash
npm run dev          # Serveur de développement
npm run build        # Build de production
npm run preview      # Prévisualisation du build
npm run lint         # Vérification ESLint
npm run analyze      # Analyse de la taille du bundle
```

## Contribution

1. Fork le repository
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit vos changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## Documentation

Des guides détaillés sont disponibles dans `docs/guides/` :
- Configuration des cookies
- Gestion des URLs et routes
- Réinitialisation mot de passe
- Traduction et internationalisation
- Optimisations de performance

## Licence

Ce projet est sous licence MIT - voir le fichier [LICENSE](LICENSE) pour plus de détails.
