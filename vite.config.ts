import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    exclude: ['lucide-react'],
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Séparation intelligente des chunks
          if (id.includes('node_modules')) {
            // Regrouper les librairies React
            if (id.includes('react') || id.includes('react-dom')) {
              return 'vendor';
            }
            // Router
            if (id.includes('react-router')) {
              return 'router';
            }
            // Charts (lazy loaded)
            if (id.includes('chart.js') || id.includes('react-chartjs')) {
              return 'charts';
            }
            // Supabase
            if (id.includes('@supabase')) {
              return 'auth';
            }
            // Utilitaires légers
            if (id.includes('lucide-react') || id.includes('react-markdown')) {
              return 'utils';
            }
            // Librairies AI lourdes (exclues pour éviter les erreurs)
            if (id.includes('langchain') || id.includes('openai') || id.includes('pinecone')) {
              return 'ai';
            }
            // Autres dépendances
            return 'vendor';
          }
        }
      },
      external: []
    },
    // Augmenter la limite de warning pour éviter le warning à 500kB
    chunkSizeWarningLimit: 1000,
    // Optimisations supplémentaires
    target: 'esnext',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  }
});
