{"name": "zest-companion", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "analyze": "npm run build && npx vite-bundle-analyzer dist"}, "dependencies": {"@supabase/supabase-js": "^2.39.3", "chart.js": "^4.4.1", "date-fns": "^4.1.0", "jspdf": "^2.5.2", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-markdown": "^9.0.1", "react-router-dom": "^7.6.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^22.15.17", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "terser": "^5.43.1", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}