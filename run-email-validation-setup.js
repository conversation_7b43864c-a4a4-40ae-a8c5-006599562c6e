const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Configuration Supabase (remplacez par vos valeurs)
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'YOUR_SUPABASE_URL';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'YOUR_SERVICE_ROLE_KEY';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupEmailValidation() {
  try {
    console.log('📧 Setting up email validation function...');

    // Lire le fichier SQL
    const sqlContent = fs.readFileSync(
      path.join(__dirname, 'docs/sql/create-email-validation-function.sql'),
      'utf-8'
    );

    // Exécuter le SQL
    const { data, error } = await supabase.rpc('exec_sql', { sql: sqlContent });

    if (error) {
      console.error('❌ Error executing SQL:', error);
      return;
    }

    console.log('✅ Email validation function created successfully');

    // Tester la fonction
    console.log('\n🧪 Testing the function...');

    const testEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    for (const email of testEmails) {
      const { data: isAllowed, error: testError } = await supabase.rpc('validate_email_allowed', {
        email_to_check: email
      });

      if (testError) {
        console.error(`❌ Error testing ${email}:`, testError);
      } else {
        console.log(`📊 ${email}: ${isAllowed ? '✅ Authorized' : '❌ Not authorized'}`);
      }
    }

  } catch (error) {
    console.error('💥 Exception:', error);
  }
}

setupEmailValidation();
